// https://v3.nuxtjs.org/api/configuration/nuxt.config
export default defineNuxtConfig({
  srcDir: 'src/',

  app: {
    head: {
      meta: [
        { name: 'viewport', content: 'width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no' }
      ],
      link: [
        // { rel: 'stylesheet', href: 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' },
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      ],
      htmlAttrs: {
        lang: 'zh-CN'
      },
    },
  },

  css: [
    'aos/dist/aos.css',
    'animate.css/animate.min.css',
    // 'bootstrap/dist/css/bootstrap.min.css',
  ],

  modules: [
    // '@unocss/nuxt',
    '@vueuse/nuxt',
    '@formkit/nuxt',
    'nuxt-svgo',
  ],

  plugins: [
    { src: '~/plugins/utilities' },
    { src: '~/plugins/bootstrap.client' },
  ],

  svgo: {
    svgoConfig: {
      removeViewBox: false,
      plugins: [
        {
          name: 'preset-default',
          params: {
            overrides: {
              removeViewBox: false,
            },
          },
        },
      ],
    },
  },

  /**
   * @see https://github.com/unocss/unocss/tree/main/packages/nuxt
   */
  unocss: {
    // presets
    uno: true, // enabled `@unocss/preset-uno`
    wind: true, // enabled `@unocss/preset-wind`
    icons: {
      extraProperties: {
        'margin-top': '-3px',
        'display': 'inline-block',
        'vertical-align': 'middle',
        // ...
      },
    }, // enabled `@unocss/preset-icons`
    attributify: true, // enabled `@unocss/preset-attributify`,

    // core options
    shortcuts: [],
    rules: [],
  },

  postcss: {
    plugins: {
      autoprefixer: {},
    },
  },

  runtimeConfig: {
    public: {
      NUXT_RAW_HOST: process.env.NUXT_RAW_HOST,
      NUXT_API_BASE: process.env.NUXT_API_BASE,
    },
  },

  vite: {
    server: {
      proxy: {
        '/raw': {
          target: 'http://localhost:3201', // 后端接口地址
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/raw/, ''),
        },
      },
    } ,
  } as any,

  compatibilityDate: '2025-04-23',
})