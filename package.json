{"name": "agilex", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@fancyapps/ui": "^4.0.31", "@formkit/i18n": "^1.0.0-beta.11", "@formkit/nuxt": "^1.0.0-beta.11", "@formkit/themes": "^1.0.0-beta.11", "@formkit/vue": "^1.0.0-beta.11", "@popperjs/core": "^2.11.6", "@unocss/nuxt": "^0.45.22", "@vueuse/components": "^9.3.0", "@vueuse/core": "^9.2.0", "@vueuse/nuxt": "^9.2.0", "animate.css": "^4.1.1", "aos": "^3.0.0-beta.6", "axios": "^1.1.3", "bootstrap": "^5.2.1", "countup.js": "^2.3.2", "swiper": "^8.4.2"}, "devDependencies": {"@iconify-json/ant-design": "^1.1.3", "@iconify-json/ion": "^1.1.3", "@unocss/preset-icons": "^0.45.22", "@unocss/preset-wind": "^0.45.22", "http-proxy-middleware": "^2.0.6", "less": "^4.1.3", "nuxt": "3.0.0-rc.10", "nuxt-svgo": "^1.0.1"}}