import { useArticle, useArticles } from './article';

export function useAbout() {
  return useArticle('about');
}

export function useRoadmap() {
  return useArticles('roadmap', true);
}

export function usePartners() {
  return useArticles('partner');
}

export function useJobs() {
  return useAxFetch<API.PagingRes<API.Job>>('/ax/jobs/page', {
    default: () => ({
      list: [],
      total: 0,
    }),
  });
}
