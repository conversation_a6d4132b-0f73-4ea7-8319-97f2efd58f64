import { useArticles } from './article';

export function useBanners() {
  return useAxFetch<API.Banner[]>('/ax/home/<USER>', {
    default: () => ([]),
  });
};

export function usePlans() {
  return useAxFetch<API.Plan[]>('/ax/home/<USER>', {
    default: () => ([]),
  });
};

export function useNoticeCase() {
  return useArticles('home-notice-case');
}

export function useProducts() {
  return useAxFetch<API.HomeProduct[]>('/ax/home/<USER>', {
    default: () => [],
    params: {
      asc: true,
      order: 'sort',
    },
  });
}
