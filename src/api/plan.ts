export function usePlan(id: number | string) {
  return useAxFetch<API.Plan>(`/ax/plan/${id}`);
}

export function usePlans() {
  return useAxFetch<API.Plan[]>('/ax/plan/list');
}

export function useProducts(id: number | string) {
  return useAxFetch<API.Product[]>(`/ax/plan/products/${id}`);
}

export function useParams(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/plan/params/${id}`);
}

export function useWidgets(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/plan/widgets/${id}`);
}

export function useAlbums(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/plan-album/list/${id}`);
}

export function useFeatures(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/plan-feature/list/${id}`);
}

export function useChassis(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/plan/chassis/${id}`);
}

export function useCases(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/plan/cases/${id}`);
}

export function useVideos(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/plan-video/list/${id}`);
}
