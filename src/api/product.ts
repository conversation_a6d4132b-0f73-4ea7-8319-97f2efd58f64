export function useProduct(id: number | string) {
  return useAxFetch<API.Product>(`/ax/product/${id}`);
}

export function useProducts() {
  return useAxFetch<API.Product[]>('/ax/product/list');
}

export function useAlbums(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/album/list/${id}`);
}

export function useFeatures(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/feature/list/${id}`);
}

export function useParams(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/param/list/${id}`);
}

export function useCases(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/case/list/${id}`);
}

export function useVideos(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/video/list/${id}`);
}

export function useWidgets(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/widget/list/${id}`);
}

export function useDocs(id: number | string) {
  return useAxFetch<API.Base[]>(`/ax/software/list/${id}`);
}
