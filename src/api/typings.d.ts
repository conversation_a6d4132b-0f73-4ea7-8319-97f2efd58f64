declare namespace API {
  type Any = Record<string, any>;

  type BaseRes<R = any> = {
    code: numer;
    data: R;
    msg: string;
  };

  type PagingParams = {
    page: number;
    limit: number;
  };

  type PagingRes<R = any> = {
    list: R[];
    total: number;
  };

  type Base = Any & {
    id: number;
  };

  type DictItem = Base & {
    dictLabel: string;
    dictValue: string;
    dictTypeId: number;
    remark: string;
  };

  type Site = Base & {
    bilibili: string;
    copyright: string;
    description: string;
    favicon: string;
    keywords: string;
    qrcode: string;
    community: string;
    recordInfo: string;
    recordUrl: string;
    title: string;
    wechat: string;
    weibo: string;
  };

  type Contact = Base & {
    address: string;
    emailHr: string;
    emailMarketing: string;
    emailSales: string;
    hotline: string;
    phone: string;
    qrSubscribe: string;
    qrSupport: string;
  };

  type Section = Base & {
    icon: string;
    name: string;
    pid: number;
    type: string;
    url: string;
    visible: number;
    children: Section[]
  };

  type Banner = Base & {
    type: number;
    cover: string;
    title: string;
    subtitle: string;
    url: string;
  };

  type Article = Base & {
    title: string;
    cover: string;
    summary: string;
    type: string;
    content: string;
    createTime: string;
  };

  type Product = Base & {
    brand: string;
    logo: string;
    cover: string;
    intro: string;
    kind: string;
    model: string;
    name: string;
    sectionId: number;
    slogan: string;
    spec: string;
    subtitle: string;
    title: string;
  };

  type HomeProduct = Base & {
    pid: number;
    title: string;
    subtitle: string;
    image: string;
    background: string;
    hasWidget: number;
  };

  type Widget = Base & {
    pid: number;
    type: string;
    title: string;
    desc: string;
    cover: string;
  };

  type Plan = Base & {
    name: string;
    cover: string;
  };

  type Job = Base & {
    title: string;
    base: string;
    email: string;
    requirements: string;
    details: string;
  };

  type News = Base & {
    cover: string;
    title: string;
    summary: string;
    createTime: string;
  };

  type Agency = Base & {
    name: string;
    type: string;
    cover: string;
    contact: string;
    phone: string;
    email: string;
    idno: string;
    gender: number;
    address: string;
  };

  type Tutorial = Base & {
    title: string;
    summary: string;
    type: string;
    cover: string;
    content: string;
    video: string;
    image: string;
  };
}
