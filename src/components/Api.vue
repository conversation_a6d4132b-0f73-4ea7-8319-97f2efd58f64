<template>
    <div class="api">
        <div class="header">
            <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
            <div class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</div>
        </div>


        <AutoLoopScroll v-if="isMobile" :speed="10" :isScrolling="true">
            <div class="api-desc">
                <div class="desc-item" v-for="(item, index) in data.desc" :key="index">{{ item.text }}</div>
            </div>
        </AutoLoopScroll>
        <div class="api-desc" v-else>
            <div class="desc-item" v-for="(item, index) in data.desc" :key="index">{{ item.text }}</div>
        </div>

        <div class="tab-bar">
            <div class="tab-item" v-for="(tab, index) in data.list" :key="index" @click="activeTab = index"
                :class="{ 'active': activeTab === index }">
                {{ tab.name }}
            </div>
        </div>

        <div class="tab-content">
            <div class="content-left content-card">
                <div class="card-top">
                    <div class="card-title">{{ curContent.title }}示例</div>
                    <div class="card-top-btn" @click="handleCopy">复制代码</div>
                    <div v-show="copySuccess" class="floating-tip">复制成功！</div>
                </div>
                <pre class="card-text">{{ curContent.code }}</pre>
            </div>
            <div class="content-right">
                <div class="card-title">{{ curContent.title }}</div>
                <div class="card-sub-title">{{ curContent.subTitle }}</div>
                <pre class="card-desc">{{ curContent.desc }}</pre>
                <div class="open-doc-btn" @click="handleOpenDoc(curContent.docUrl)">
                    <span>查看完整的API文档</span>
                    <i class="icon-right-arrow"></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                title: '',
                desc: '',
                topCards: [],
                bottomCards: []
            }
        }
    }
})

const activeTab = ref(0);
const copySuccess = ref(false);
const isMobile = ref(false);
const handleCopy = async () => {
    try {
        await navigator.clipboard.writeText(curContent.value.code);
        copySuccess.value = true;
        setTimeout(() => {
            copySuccess.value = false;
        }, 1000);
    } catch (err) {
        console.error('复制失败:', err);
    }
};
const curContent = computed(() => props.data.list[activeTab.value].content);

const handleOpenDoc = (href: string) => {
    window.open(href, '_blank');
};

onMounted(() => {
    isMobile.value = window.innerWidth < 768;
});
</script>

<style scoped lang="less">
.floating-tip {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    transition: opacity 0.3s;
    animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-50%);
    }

    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.floating-tip[aria-hidden="true"] {
    opacity: 0;
}

.api {
    width: 100%;
    padding: 0 20px 100px;
    margin-bottom: var(--component-mb);
    background-color: #fff;
    /* 保持原有样式不变 */

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding-top: 100px;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            max-width: 1260px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .api-desc {
        display: flex;
        align-items: center;
        justify-content: space-around;
        max-width: 1440px;
        margin: 0 auto 40px;
        gap: 10px;

        .desc-item {
            display: flex;
            align-items: center;
            color: rgba(0, 0, 0, 0.80);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 300;
            line-height: 21px;

            &::before {
                display: inline-block;
                content: "";
                width: 20px;
                height: 20px;
                margin-right: 12px;
                background: url('/assets/imgs/icon_check_blue.svg') no-repeat;
                background-size: 100% 100%;
                background-position: center;
            }
        }
    }

    .tab-bar {
        max-width: 1440px;
        margin: 0 auto 40px;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #D8D8D8;
        gap: 40px;

        .tab-item {
            display: inline-flex;
            height: 56px;
            padding: 0 24px;
            justify-content: center;
            align-items: center;
            color: rgba(0, 0, 0, 0.80);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 18px;
            font-weight: var(--sub-title-font-weight);
            line-height: 27px;
            cursor: pointer;
            position: relative;
            white-space: nowrap;

            &.active {
                color: #DF0428;
                font-weight: var(--title-font-weight);

                &::after {
                    content: '';
                    display: block;
                    width: 100%;
                    height: 1px;
                    background: #DF0428;
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                }
            }
        }
    }

    .tab-content {
        max-width: 1440px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        margin: 0 auto;

        .content-left {
            display: flex;
            flex-direction: column;
            height: 596px;
            border-radius: 12px;
            background: #131826;
            overflow: hidden;

            .card-top {
                display: flex;
                height: 56px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.12);
                background: #1F2431;
                padding: 0 24px;

                .card-title {
                    color: rgba(255, 255, 255, 0.97);
                    font-family: "PingFang SC";
                    font-size: 16px;
                    font-weight: var(--title-font-weight);
                    line-height: 24px;
                    margin-top: 19px;
                }

                .card-top-btn {
                    --color-: rgba(255, 255, 255, 0.57);
                    display: flex;
                    align-items: center;
                    align-self: center;
                    color: var(--color-);
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 21px;
                    margin-left: auto;
                    cursor: pointer;

                    &::before {
                        content: "";
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        mask-image: url(/assets/imgs/icon_copy.svg);
                        mask-repeat: no-repeat;
                        mask-size: 100%;
                        mask-position: center;
                        background-color: var(--color-);
                        margin-right: 4px;
                    }

                    &:hover {
                        --color-: #fff;
                    }
                }
            }

            .card-text {
                color: rgba(255, 255, 255, 0.57);
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 21px;
                padding: 12px 24px;
                overflow: auto;
            }
        }

        .content-right {
            border-radius: 12px;
            background-color: #eee;
            padding: 24px;

            .card-title {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 18px;
                font-style: normal;
                font-weight: 400;
                line-height: 27px;
                margin-bottom: 12px;
            }

            .card-sub-title {
                color: rgba(0, 0, 0, 0.65);
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 21px;
                margin-bottom: 18px;
            }

            .card-desc {
                color: rgba(0, 0, 0, 0.65);
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 33px;
                padding: 0;
                white-space: pre-wrap;
                margin-bottom: 27px;
            }

            .open-doc-btn {
                display: flex;
                align-items: center;
                justify-self: flex-start;
                color: #1677FF;
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 21px;
                cursor: pointer;

                .icon-right-arrow {
                    background-color: #1677FF;
                    margin-left: 8px;
                    transition: all 0.3s ease-in-out;
                }

                &:hover {
                    .icon-right-arrow {
                        margin-left: 18px;
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .api {
        padding: 0 16px 32px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }

        .api-desc {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 4px auto 12px;
            gap: 16px;

            .desc-item {
                height: 40px;
                white-space: nowrap;
                margin-right: 16px;

                &::before {
                    width: 16px;
                    height: 16px;
                    margin-right: 8px;
                }
            }
        }

        .tab-bar {
            flex-wrap: wrap;
            justify-content: flex-start;
            margin: 0 auto 12px;
            border-bottom: none;
            gap: 16px;

            .tab-item {
                height: 40px;
                padding: 0 10px;
                font-size: 14px;
                line-height: 21px;
            }
        }

        .tab-content {
            grid-template-columns: repeat(1, 1fr);
            gap: 12px;

            .content-left {
                border-radius: 6px;

                .card-top {
                    padding: 0 12px;

                    .card-title {
                        font-size: 14px;
                        line-height: 24px;
                        margin-top: 19px;
                    }
                }

                .card-text {
                    font-size: 12px;
                    padding: 12px 12px 24px;
                    overflow: auto;
                }
            }

            .content-right {
                border-radius: 6px;
                padding: 12px 12px 34px;

                .card-title {
                    font-size: 14px;
                    line-height: 27px;
                    margin-bottom: 8px;
                    font-weight: var(--title-font-weight);
                }

                .card-sub-title {
                    font-size: 12px;
                    line-height: 18px;
                    margin-bottom: 8px;
                }

                .card-desc {
                    color: rgba(0, 0, 0, 0.65);
                    font-family: "PingFang SC";
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 27px;
                    padding: 0;
                    white-space: pre-wrap;
                    margin-bottom: 17px;
                }

                .open-doc-btn {
                    display: flex;
                    align-items: center;
                    justify-self: flex-start;
                    color: #1677FF;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 21px;
                    cursor: pointer;

                    .icon-right-arrow {
                        background-color: #1677FF;
                        margin-left: 8px;
                        transition: all 0.3s ease-in-out;
                    }

                    &:hover {
                        .icon-right-arrow {
                            margin-left: 18px;
                        }
                    }
                }
            }
        }
    }
}
</style>