<template>
    <div class="application-scenarios">
        <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
        <p class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</p>
        <div class="card-grid">
            <div class="scene-card" v-for="(scenario, index) in data.scenarios" :key="index">
                <img :src="patchRawUrl(scenario.imageUrl.url)" :alt="scenario.title" class="scene-img" />
                <div class="card-info">
                    <h3 class="card-title">{{ scenario.title }}</h3>
                    <p class="card-desc">{{ scenario.description }}</p>
                </div>
                <i class="icon_plus_circle" @click="handleShowModal(scenario.scenarioDetail)"></i>
            </div>
        </div>
    </div>
    <div class="scenario-detail-modal" v-if="modalVisible" @click="handleHideModal">
        <ScenarioDetail :data="scenarioDetailData" @hideModal="handleHideModal" @click.stop></ScenarioDetail>
    </div>
</template>

<script setup lang="ts">
const patchRawUrl = usePatchRawUrl();

const props = defineProps({
    data: {
        type: Object as () => {
            title: string
            subTitle: string
            textColor: string
            scenarios: Array<{
                title: string
                description: string
                imageUrl: {
                    url: string,
                    type: string
                },
                scenarioDetail: {}
            }>
        },
        required: true
    }
})

const modalVisible = ref(false);
const scenarioDetailData = ref({});
const handleShowModal = (data) => {
    modalVisible.value = true;
    scenarioDetailData.value = data;
}

const handleHideModal = () => {
    modalVisible.value = false;
}


</script>

<style lang="less" scoped>
.application-scenarios {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 1440px;
    // padding: 40px 20px;
    margin: 0 auto;
    margin-bottom: var(--component-mb);

    .title {
        max-width: 816px;
        font-family: "PingFang SC";
        font-size: 48px;
        font-style: normal;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        text-align: center;
        color: var(--title-theme-color);
    }

    .sub-title {
        max-width: 1260px;
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: 400;
        line-height: 36px;
        text-align: center;
        color: var(--sub-title-theme-color);
        margin: 24px 0 40px;
    }

    .card-grid {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        margin: 0 auto;

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }
    }

    .scene-card {
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        position: relative;

        .scene-img {
            display: inline-block;
            width: 100%;
            height: 392px;
            object-fit: cover;
            transform-origin: center;
        }

        .card-info {
            padding: 24px;

            .card-title {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 24px;
                font-weight: var(--title-font-weight);
                line-height: 40px;
                margin-bottom: 12px;
            }

            .card-desc {
                color: var(--, rgba(0, 0, 0, 0.65));
                font-family: "PingFang SC";
                font-size: 16px;
                font-weight: 400;
                line-height: 22px;
            }
        }

        .icon_plus_circle {
            position: absolute;
            bottom: 24px;
            right: 24px;
            border-radius: 56px;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                background-color: rgba(0, 0, 0, 0.80);
            }
        }

        .icon_plus_circle {
            display: inline-block;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none"><circle cx="28" cy="28" r="28" fill="black" fill-opacity="0.2"/> <rect x="16" y="26" width="24" height="3" rx="1.5" fill="white"/> <rect x="29" y="16" width="24" height="3" rx="1.5" transform="rotate(90 29 16)" fill="white"/> </svg>');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 56px;
            height: 56px;
        }
    }
}

.scenario-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    touch-action: none;
    z-index: 9999;
}

@media (max-width: 768px) {
    .application-scenarios {
        width: calc(100% - 32px);
        // margin-bottom: 40px;

        .card-grid {
            gap: 16px;
        }

        .title {
            font-size: 18px;
            line-height: 27px;
            margin-bottom: 8px;
        }

        .sub-title {
            font-size: 14px;
            line-height: 21px;
            margin-top: 0;
            margin-bottom: 12px;
        }

        .scene-card {
            line-height: 0;
            border-radius: 6px;
            padding: 0;
        }

        .scene-card .scene-img {
            aspect-ratio: 361/293;
            width: 100%;
            height: initial;
        }

        .scene-card .card-info .card-desc {
            display: none;
        }

        .scene-card .card-info {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 24px;
            position: absolute;
            z-index: 1;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;

            .card-title {
                color: rgba(255, 255, 255, 0.97);
                font-size: 16px;
                line-height: 21px;
                margin: 0 16px;
            }
        }

        .scene-card .icon_plus_circle {
            width: 40px;
            height: 40px;
            border-radius: 40px;
            background-color: rgba(0, 0, 0, 0.80);
            right: 16px;
            bottom: 16px;
            z-index: 2;
        }
    }
}
</style>