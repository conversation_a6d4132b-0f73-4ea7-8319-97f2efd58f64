<template>
  <div class="scroll-container" ref="containerRef">
    <div class="scroll-content" ref="contentRef" :style="contentStyle">
      <slot />
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { defineProps } from 'vue'

const containerRef = ref(null)
const contentRef = ref(null)
const shouldScroll = ref(false)

const props = defineProps({
  speed: {
    type: Number,
    default: 5,
    description: '滚动速度（秒/周期）'
  },
  isScrolling: {
    type: Boolean,
    default: true,
    description: '是否启用滚动（true=滚动，false=暂停）'
  }
})

const updateShouldScroll = () => {
  if (containerRef.value && contentRef.value) {
    const containerWidth = containerRef.value.offsetWidth
    const contentWidth = contentRef.value.offsetWidth/2
    shouldScroll.value = contentWidth > containerWidth
  }
}

onMounted(() => {
  updateShouldScroll()
})

watch(() => props.isScrolling, updateShouldScroll)

const contentStyle = computed(() => {
  const style = {}
  if (shouldScroll.value) {
    style.animationDuration = `${props.speed}s`
    style.animationPlayState = props.isScrolling ? 'running' : 'paused'
  } else {
    style.animation = 'none'
  }
  return style
})
</script>

<style scoped>
.scroll-container {
  overflow: hidden;
  white-space: nowrap;
}

.scroll-content {
  display: inline-flex;
  align-items: center;
  animation: loop linear infinite;
  /* 双倍宽度实现无缝循环 */
  /* width: 200%; */
}

@keyframes loop {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-50%); /* 移动50%即可完成一个完整周期（因为内容是双倍宽度） */
  }
}
</style>