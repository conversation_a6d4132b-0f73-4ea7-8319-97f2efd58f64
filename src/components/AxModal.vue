<template>
  <div ref="modal" class="ax-modal" v-show="visible">
    <div class="ax-modal-dialog">
      <div class="ax-modal-content">
        <div class="ax-modal-header">
          <h1 class="ax-modal-title" v-if="title">{{ title }}</h1>
          <button type="button" class="ax-modal-close-btn" data-bs-dismiss="modal" aria-label="Close" @click="hide">
            &times;
          </button>
        </div>
        <div class="ax-modal-body">
          {{ message }}
        </div>
        <div class="ax-modal-footer">
          <button v-if="showCancel" type="button" class="ax-modal-btn ax-modal-cancel-btn" @click="hide">
            {{ cancelText }}
          </button>
          <button v-if="showConfirm" type="button" class="ax-modal-btn ax-modal-confirm-btn" @click="hide">
            {{ confirmText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="AxModal">
export type AxModalProps = {
  title?: string;
  message?: string;
  centered?: boolean;
  showCancel?: boolean;
  showConfirm?: boolean;
  cancelText?: string;
  confirmText?: string;
};

withDefaults(defineProps<AxModalProps>(), {
  centered: true,
  showConfirm: true,
  cancelText: '取消',
  confirmText: '确认',
});

const emit = defineEmits<{
  (e: 'onHide', ev: Event): void;
  (e: 'onShow', ev: Event): void;
  (e: 'onShown', ev: Event): void;
  (e: 'onHidden', ev: Event): void;
  (e: 'onCancel'): void;
  (e: 'onConfirm'): void;
}>();

const modal = ref<HTMLDivElement>();
const visible = ref(false);

const hide = async () => {
  visible.value = false;
}

const show = async () => {
  visible.value = true;
}

defineExpose({ hide, show });

</script>
<style lang="less" scoped>
.ax-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.ax-modal-dialog {
  max-width: 500px;
  width: 90%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.ax-modal-content {
  padding: 20px;
}

.ax-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.ax-modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.ax-modal-close-btn {
  font-size: 24px;
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  transition: color 0.3s;
}

.ax-modal-close-btn:hover {
  color: #333;
}

.ax-modal-body {
  font-size: 24px;
  font-weight: bold;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.ax-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.ax-modal-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.ax-modal-cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.ax-modal-confirm-btn {
  background: linear-gradient(180deg, #000 -42.86%, #555 100%);
  color: #fff;
}

.ax-modal-cancel-btn:hover {
  background-color: #e0e0e0;
}

.ax-modal-confirm-btn:hover {
  background: linear-gradient(180deg, #000 -42.86%, #333 100%);
}
</style>