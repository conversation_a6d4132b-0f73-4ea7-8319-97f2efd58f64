<template>
  <div class="province-select input-container select-container">
    <label class="label">
      <span class="required">*</span>所在省份
    </label>
    <div class="dropdown">
      <div class="selected" @click="toggleDropdown($event)">
        {{ selectedProvince || '请选择省份' }}
        <span class="arrow" :class="{ open: isOpen }">▾</span>
      </div>
      <ul v-if="isOpen" class="options">
        <li
          v-for="province in provinces"
          :key="province"
          @click.stop="selectProvince(province)"
          :class="{ selected: province === selectedProvince }"
        >
          {{ province }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const provinces = [
  '北京市',
  '河北省',
  '河南省',
  '四川省',
  '广东省',
  '山东省',
  '江苏省',
  '浙江省',
]

const selectedProvince = ref('')
const isOpen = ref(false)

function toggleDropdown(event) {
  event.stopPropagation(); // 阻止事件冒泡
  isOpen.value = !isOpen.value
  console.log(1,isOpen.value);
}

function selectProvince(province) {
  console.log(2,isOpen.value);
  selectedProvince.value = province
  isOpen.value = false
}

// 点击页面其他区域关闭下拉框
document.addEventListener('click', () => {
  console.log(3,isOpen.value);
  isOpen.value = false
})
</script>

<style scoped>
.province-select {
  width: 250px;
  font-family: Arial, sans-serif;
}

.label {
  font-size: 14px;
  margin-bottom: 4px;
  display: block;
}

.required {
  color: red;
  margin-right: 4px;
}

.dropdown {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 12px;
  position: relative;
  cursor: pointer;
  user-select: none;
  background-color: #fff;
}

.selected {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arrow {
  transition: transform 0.3s ease;
}

.arrow.open {
  transform: rotate(180deg);
}

.options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.options li {
  padding: 10px;
  cursor: pointer;
}

.options li:hover {
  background-color: #f2f2f2;
}

.options li.selected {
  background-color: #e6f7ff;
  font-weight: bold;
}
</style>