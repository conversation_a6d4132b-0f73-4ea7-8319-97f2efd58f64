<template>
    <div ref="banner" class="banner swiper" v-if="data.type == 'swiper'">
        <div class="swiper-wrapper">
            <div class="swiper-slide banner-item" v-for="(item, index) in data.list" :key="index">
                <div class="banner-item-center anim-group">
                    <div class="title anim-item anim1" :style="{ color: item.textColor || data.textColor || '#fff' }">
                        {{ item.title }}
                    </div>
                    <div class="sub-title anim-item anim2"
                        :style="{ color: item.textColor || data.textColor || '#fff' }">
                        {{ item.subTitle }}
                    </div>
                    <div class="btn-more anim-item anim3">
                        <div class="keep-btn-left" 
                            :style="{ borderColor: item.textColor || data.textColor || '#fff' }">
                            <a class="btn-text" :href="item.jumpLink"
                                :style="{ color: item.textColor || data.textColor || '#fff' }">了解更多</a>
                            <i class="btn-more-icon icon-right-arrow"
                                :style="{ backgroundColor: item.textColor || data.textColor || '#fff' }"></i>
                        </div>
                    </div>
                </div>
                <img :data-src="patchRawUrl(item.resource.url)" alt="" class="banner-img swiper-lazy"
                    v-if="item.resource.type === 'image'">
                <video ref="video" :data-src="$patchRawUrl(item.resource.url)" class="banner-img video swiper-lazy"
                    muted v-if="item.resource.type === 'video'">
                </video>
                <div class="swiper-lazy-preloader"></div>
            </div>
        </div>
        <template v-if="data.list.length > 1">
            <div class="swiper-pagination"></div>
            <div class="swiper-btn swiper-btn-prev">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M6 17L13 10L6 3" :stroke="data.textColor || '#fff'" stroke-opacity="0.97"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>
            <div class="swiper-btn swiper-btn-next">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M6 17L13 10L6 3" :stroke="data.textColor || '#fff'" stroke-opacity="1"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>
        </template>
    </div>

    <template v-else>
        <div class="banner one-item" :class="{ first: index == 0 }" v-for="(item, index) in data.list" :key="index">
            <div class="banner-item">
                <div class="banner-item-center anim-group">
                    <div class="title anim-item anim1" :style="{ color: item.textColor || data.textColor || '#fff' }">
                        {{ item.title }}
                    </div>
                    <div class="sub-title anim-item anim2"
                        :style="{ color: item.textColor || data.textColor || '#fff' }">
                        {{ item.subTitle }}
                    </div>
                    <div class="btn-more anim-item anim3">
                        <div class="keep-btn-left" 
                            :style="{ borderColor: item.textColor || data.textColor || '#fff' }">
                            <a class="btn-text" :href="item.jumpLink"
                                :style="{ color: item.textColor || data.textColor || '#fff' }">了解更多</a>
                            <i class="btn-more-icon icon-right-arrow"
                                :style="{ backgroundColor: item.textColor || data.textColor || '#fff' }"></i>
                        </div>
                    </div>
                </div>
                <img :src="patchRawUrl(item.resource.url)" alt="" class="banner-img"
                    v-if="item.resource.type === 'image'">
                <div class="video-container banner-img" v-if="item.resource.type === 'video'"
                    @click="togglePlay($event, index)">
                    <div class="play-btn" v-show="!isPlaying[index]"></div>
                    <video ref="video" :src="patchRawUrl(item.resource.url)" class="video" muted loop>
                    </video>
                </div>
            </div>
        </div>
    </template>
</template>
<script setup lang='ts'>
import { Navigation, Pagination, Autoplay, Lazy } from 'swiper';

const patchRawUrl = usePatchRawUrl();

//props
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                type: 'swiper', // swiper or one-item
                list: []
            }
        }
    }
})

// 根据文本颜色设置按钮样式
const getButtonStyle = (color) => {
    if (!color || color === '#fff') {
        return {}; // 默认白色样式
    }
    // 自定义颜色样式
    return {
        borderColor: color,
    };
};

// 获取SVG边框颜色
const getSvgStrokeColor = (color) => {
    // 可以根据需要添加逻辑来改变SVG颜色
    if (!color || color === '#fff') {
        return 'white'; // 默认白色图标
    }
    return color;
};

const activeIndex = ref(0);
const banner = ref<HTMLDivElement>();
if (props.data.list.length > 1) {
    useSwiper(banner, {
        modules: [Navigation, Pagination, Autoplay, Lazy],
        loop: true,
        speed: 500,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        },
        // 新增懒加载配置
        lazy: {
            loadPrevNext: true,
            loadPrevNextAmount: 1,
            loadOnTransitionStart: true,
        },
        preloadImages: false, // 必须设置为false
        pagination: {
            el: '.banner .swiper-pagination',
        },
        navigation: {
            prevEl: '.banner .swiper-btn-prev',
            nextEl: '.banner .swiper-btn-next',
        },
        on: {
            slideChangeTransitionEnd: (swiper) => {
                activeIndex.value = swiper.realIndex;

                // 清除所有视频状态
                const container = swiper.el;
                const prevVideos = container.querySelectorAll('video');
                prevVideos.forEach(video => {
                    video.onended = null;
                    video.pause();
                });

                const videoElement = container.querySelector('.swiper-slide-active video') as HTMLVideoElement;
                if (videoElement) {
                    swiper.autoplay.stop();
                    videoElement.currentTime = 0;
                    videoElement.play().catch(() => { });
                    videoElement.onended = () => {
                        // swiper.autoplay.start();
                        swiper.slideNext();
                    };
                } else {
                    swiper.autoplay.start();
                }

            }
        }
    });
}

const isPlaying = ref([]);

const togglePlay = (event, index) => {
    const container = event.currentTarget;
    const video = container.querySelector('video');
    if (video) {
        if (video.paused) {
            video.play();
            isPlaying.value[index] = true;
        } else {
            video.pause();
            isPlaying.value[index] = false;
        }
    }
};
</script>
<style lang="less" scoped>
.title,
.sub-title,
.btn-text {
    font-family: Open Sans, PingFang SC, Microsoft YaHei, Helvetica Neue, Hiragino Sans GB, WenQuanYi Micro Hei, Arial, "sans-serif";
}

.banner {
    margin-bottom: var(--component-mb);

    .banner-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        // height: 100vh;
        min-height: 600px;
        aspect-ratio: 32/15;
        position: relative;

        .banner-item-center {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            margin-top: 104px;
        }



        .title {
            width: 600px;
            text-align: center;
            color: #fff;
            font-size: 40px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 60px;
            margin-top: 60px;
            position: relative;
            z-index: 1;
        }


        .sub-title {
            max-width: 1200px;
            text-align: center;
            color: #fff;
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 12px;
            padding: 0 40px;
            position: relative;
            z-index: 1;
        }

        .btn-more {
            width: 130px;
            height: 40px;
            z-index: 1;
            margin-top: 24px;

            .keep-btn-left {
                display: flex;
                width: 130px;
                height: 40px;
                padding: 8px 16px;
                justify-content: space-between;
                align-items: center;
                gap: 12px;
                flex-shrink: 0;
                border-radius: 60px;
                border: 1px solid #FFF;
                cursor: pointer;
                position: relative;
                transition: all 0.1s ease-in-out;

                .btn-text {
                    color: #FFF;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 300;
                    line-height: 24px;
                }

                .btn-more-icon {
                    width: 20px;
                    height: 20px;
                }
            }

            &:hover .keep-btn-left {
                width: 140px;
                background: rgba(255, 255, 255, 0.2);
            }

        }

        .banner-img {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;
            cursor: pointer;
            object-fit: cover;
        }

        .play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 1;
        }

        .video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }


    }

    &.one-item .banner-item .banner-item-center {
        margin-top: 0;
    }

    &.one-item.first .banner-item .banner-item-center {
        margin-top: 104px;
    }

    .swiper-pagination {
        height: 24px;
        position: absolute;
        bottom: 48px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1;
    }

    .swiper-btn {
        display: none;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease-in-out;

        &:hover {
            background: rgba(0, 0, 0, 0.40);
        }
    }

    &:hover .swiper-btn {
        display: flex;
    }

    .swiper-btn-prev,
    .swiper-btn-next {
        width: 60px;
        height: 100px;
        border-radius: 12px 0 0 12px;
        background: rgba(0, 0, 0, 0.20);
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        z-index: 1;
    }

    .swiper-btn-prev {
        left: 0;
        transform: translateY(-50%) rotate(180deg);
    }

    .swiper-btn-next {
        right: 0;
    }
}

.swiper {
    --swiper-pagination-bullet-opacity: 1;
    --swiper-pagination-bullet-inactive-opacity: 1;

    .swiper-pagination :deep(.swiper-pagination-bullet) {
        width: 17px;
        height: 2px;
        border-radius: 2px;
        transition: all .3s ease-in;
        background-color: rgba(255, 255, 255, 0.5);
    }

    .swiper-pagination :deep(.swiper-pagination-bullet-active) {
        background-color: #fff;
    }
}

.anim-group .anim-item {
    animation-fill-mode: both;
    animation-duration: 800ms;
}

.anim-group.active .anim-item,
.swiper-slide-active .anim-item {
    animation-name: fadeInUp;
}

.anim-group .anim1 {
    animation-delay: 500ms;
}

.anim-group .anim2 {
    animation-delay: 800ms;
}

.anim-group .anim3 {
    animation-delay: 1200ms;
}

@media (max-width: 768px) {
    .banner {
        // margin-bottom: 16px;
    }

    .banner .banner-item .banner-item-center {
        margin-top: 0;
    }

    .banner .banner-item .title {
        // height: 24px;
        font-size: 16px;
        line-height: 24px;
        margin-top: 40px;
    }

    .banner .banner-item .sub-title {
        // height: 21px;
        font-size: 14px;
        line-height: 21px;
        margin-top: 4px;
        padding: 0 16px;
    }

    .banner .banner-item .btn-more {
        height: 28px;
        padding: 8px 12px;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        margin-top: 8px;

        .keep-btn-left,
        &:hover .keep-btn-left {
            width: 110px;
            height: 28px;
            padding: 8px 12px;
            justify-content: center;

            .btn-text {
                color: var(--, rgba(255, 255, 255, 0.97));
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 18px;
            }
        }

        // &:hover .keep-btn-left {
        //     width: 140px;
        //     background: rgba(255, 255, 255, 0.2);
        // }
    }

    .banner:hover .swiper-btn {
        display: none;
    }

    // .banner .swiper-pagination{
    //     display: none;
    // }

}
</style>