<template>
    <div ref="banner" class="banner2">
        <div class="title" :style="{ color: data.textColor }" v-if="data.title">{{ data.title }}</div>
        <div class="sub-title" :style="{ color: data.textColor }" v-if="data.subTitle">{{ data.subTitle }}</div>
        <img :src="patchRawUrl(data.resource.url)" alt="" class="banner-img" v-if="data.resource.type === 'image'">
        <div class="video-container banner-img" v-if="data.resource.type === 'video'" @click="togglePlay">
            <div class="play-btn" @click.stop="togglePlay" v-show="!isPlaying"></div>
            <video ref="video" :src="patchRawUrl(data.resource.url)" class="video" muted loop>
            </video>
        </div>
    </div>
</template>
<script setup lang='ts'>

const patchRawUrl = usePatchRawUrl();

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
})

const isPlaying = ref(false);

const banner = ref<HTMLElement>();
const togglePlay = () => {
    const video = banner.value?.querySelector('video');
    if (video) {
        if (video.paused) {
            video.play();
            setTimeout(() => {
                isPlaying.value = true;
            }, 1000);
        } else {
            video.pause();
            isPlaying.value = false;
        }
    }
};
</script>
<style lang="less" scoped>
.banner2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    min-height: 600px;
    position: relative;
    // margin-bottom: 24px;
    margin-bottom: var(--component-mb);
    // cursor: pointer;

    .title {
        text-align: center;
        color: rgba(0, 0, 0, 0.8);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 48px;
        font-style: normal;
        font-weight: var(--title-font-weight);
        line-height: 72px;
    }

    .sub-title {
        text-align: center;
        color: rgba(0, 0, 0, 0.65);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: var(--sub-title-font-weight);
        line-height: 36px;
        margin-top: 24px;
        margin-bottom: 40px;
    }

    .banner-img {
        width: 100%;
        height: 100%;
        aspect-ratio: 12/5;
        object-fit: cover;
        cursor: pointer;
        position: relative;
    }

    .play-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
        z-index: 1;
    }

    .video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

@media (max-width: 768px) {
    .banner2 {
        height: initial;
        min-height: initial;
        // margin-bottom: 32px;
        cursor: pointer;

        .title {
            font-size: 18px;
            line-height: 27px;
        }

        .sub-title {
            font-size: 14px;
            line-height: 21px;
            margin-top: 8px;
            margin-bottom: 12px;
        }

        .banner-img {
            width: 100%;
            // height: 200px;
            height: initial;
            aspect-ratio: 393/200;
            object-fit: cover;
            cursor: pointer;
            position: relative;
        }

        .play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 1;
        }

        .video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}
</style>