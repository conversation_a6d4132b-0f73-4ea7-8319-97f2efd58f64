<template>
    <div class="big-banner" v-if="data.list">
        <div class="banner-title" :style="{ color: data.textColor }">{{ data.title }}</div>
        <div class="banner-desc" :style="{ color: data.textColor }">
            {{ data.desc }}
        </div>
        <div class="banner-resource" @mousemove="handleMouseMove" @click="handleClickMouseBtn($event, activeIndex)">
            <div ref="resourceContainer" class="swiper">
                <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="(item, index) in data.list" :key="index">
                        <div class="resource-container">
                            <i class="resource-loading"></i>
                            <img class="resource" :src="$patchRawUrl(item.resource.url)" alt="" v-if="item.resource.type === 'image'">
                            <video class="resource video" :class="{ active: activeIndex == index }"
                                :src="$patchRawUrl(item.resource.url)" v-if="item.resource.type === 'video'" loop
                                @mouseenter="handleVideoPlay($event, index)"
                                @mouseleave="handleVideoPause($event, index)" muted></video>
                        </div>
                    </div>
                </div>
            </div>
            <i class="swiper-btn mouse-btn"></i>
            <!-- <template v-if="data.list.length > 1">
                <div ref="paginationElm" class="swiper-pagination" :class="componentRandomId"></div>
            </template> -->
        </div>
        <div ref="bannerResourceSwiperTab" class="banner-resource-swiper-tab">
            <div class="tab-item" :class="{ active: activeIndex == index }" v-for="(item, index) in data.list"
                :key="index" @click="handleSlideTo(index)">{{ item.name }}</div>
        </div>
        <div class="banner-sub-desc">{{ data.list[activeIndex].subDesc }}</div>
    </div>
</template>
<script setup lang='ts'>
// import { Swiper, SwiperSlide } from 'swiper/vue';
// import { Navigation, Pagination, Autoplay } from 'swiper';

const isMobile = ref(false);

onMounted(() => {
    isMobile.value = window.innerWidth < 768;
})

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: []
            }
        }
    }
})

const resourceContainer = ref<HTMLElement>();
const bannerResourceSwiperTab = ref<HTMLElement>();

let swiperInstance = ref();

useSwiper(resourceContainer, {
    // modules: [Pagination],
    lazy: {
        loadPrevNext: true,
    },
    speed: 1000,
    loop: true,
    slidesPerView: 'auto',
    loopedSlides: 2,
    initialSlide: 0,
    spaceBetween: 24,
    centeredSlides: true,
    // pagination: {
    //     el: '.banner-resource .swiper-pagination',
    // },
    on: {
        init: (swiper) => {
            swiperInstance.value = swiper;
        },
        slideChangeTransitionStart: (event) => {
            if (curVideo.value) {
                curVideo.value.pause();
            }

        },
        slideChangeTransitionEnd: (event) => {
            activeIndex.value = event.realIndex;

            const container = event.el;
            const videoElement: HTMLVideoElement = container.querySelector('.swiper-slide-active video');
            if (videoElement) {
                curVideo.value = videoElement;
                videoElement.play().catch(() => { });
            }
        }
    }
});

const curVideo = ref(null);
let activeIndex = ref(0);
const handleSlideTo = (index: number) => {
    swiperInstance.value.slideToLoop(index);
    activeIndex.value = index;
};

watch(activeIndex, (newVal) => {
    const container = bannerResourceSwiperTab.value as HTMLElement;
    const activeTab = container.querySelectorAll('.tab-item')[newVal];
    if (activeTab) {
        activeTab.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
        });
    }
});
const handleVideoPlay = (e: Event, index: number) => {
    if (activeIndex.value !== index) {
        return;
    }
    const video = e.currentTarget as HTMLVideoElement;
    video.play().catch(() => { });
};

const handleVideoPause = (e: Event, index: number) => {
    const video = e.currentTarget as HTMLVideoElement;
    video.pause();
};

const handleMouseMove = (e: MouseEvent) => {
    console.log(window.innerWidth);
    //移动端不使用
    if (isMobile.value) {
        return;
    }
    const container = e.currentTarget as HTMLElement;
    const { width, left, top } = container.getBoundingClientRect();
    const mouseBtn = container.querySelector('.mouse-btn') as HTMLDivElement;
    if (!mouseBtn) return;
    const clientX = e.clientX;
    const clientY = e.clientY;

    const relativeX = clientX - left;
    const relativeY = clientY - top;


    mouseBtn.style.left = `${relativeX - 28}px`;
    mouseBtn.style.top = `${relativeY - 28}px`;

    if (relativeX < width / 2) {
        mouseBtn.classList.add('left');
        mouseBtn.classList.remove('right');
    } else {
        mouseBtn.classList.add('right');
        mouseBtn.classList.remove('left');
    }
};

const handleClickMouseBtn = (e: MouseEvent, index: number) => {
    if (isMobile.value) {
        return;
    }
    if (activeIndex.value !== index) {
        return;
    }
    const container = e.currentTarget as HTMLElement;
    const { width, left } = container.getBoundingClientRect();
    const clientX = e.clientX;
    const relativeX = clientX - left;

    if (relativeX < width / 2) {
        swiperInstance.value.slidePrev();
    } else {
        swiperInstance.value.slideNext();
    }
}

</script>
<style lang="less" scoped>
.big-banner {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    position: relative;
    margin-bottom: var(--component-mb);

    .banner-title {
        max-width: 816px;
        font-family: "PingFang SC";
        font-size: 48px;
        font-style: normal;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        text-align: center;
        color: rgba(0, 0, 0, 0.8);

    }

    .banner-desc {
        max-width: 1260px;
        height: 72px;
        flex-shrink: 0;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: var(--sub-title-font-weight);
        line-height: 36px;
        margin-top: 24px;
        margin-bottom: 40px;
        color: rgba(0, 0, 0, 0.65);
    }

    .banner-resource {
        width: 100%;
        height: 664px;
        position: relative;
        cursor: none;

        .swiper-btn {
            display: block;
            width: 56px;
            height: 56px;
            background: url('../assets/imgs/arrow_btn.png') no-repeat center center;
            background-size: 100% 100%;
            position: absolute;
            opacity: 0;
            visibility: hidden;
            // transition: all 0.3s ease;
            z-index: 10;
            top: 50%;
            pointer-events: none;

            &.active {
                // opacity: 1;
                visibility: visible;
            }

            &.left {
                // left: 50px;
                transform: rotate(180deg);
            }
        }

        &:hover {
            .swiper-btn {
                opacity: 1;
                visibility: visible;
            }
        }

        .resource-container {
            display: flex;
            align-items: center;
            width: 1440px;
            height: 664px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            cursor: none;

            .resource {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }


        }

        :deep(.swiper-slide) {
            width: 1440px;
            // transform: scale(0.88);
            // transition: all 0.3s ease;

            // &.swiper-slide-active  {
            //     transform: scale(1);
            // }
        }
    }



    .banner-resource-swiper-tab {
        display: flex;
        text-align: center;
        margin-top: 43px;
        margin-bottom: 24px;
        border-bottom: 2px solid #D9D9D9;

        .tab-item {
            width: 226px;
            height: 53px;
            margin-right: 62px;
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: 27px;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.65);
            position: relative;

            &:nth-last-child(1) {
                margin-right: 0;
            }

            &.active {
                color: rgba(0, 0, 0, 0.80);
                line-height: 27px;
                font-weight: 500;
                cursor: initial;

                &::after {
                    content: '';
                    display: block;
                    width: 100%;
                    height: 2px;
                    background: #DF0428;
                    margin-top: 24px;
                    position: absolute;
                    bottom: -2px;
                }
            }
        }

    }

    .banner-sub-desc {
        max-width: 1260px;
        color: rgba(0, 0, 0, 0.65);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 27px;
    }
}

@media (max-width: 768px) {
    .big-banner {
        // margin-bottom: 32px;
        overflow: hidden;

        .banner-title {
            font-size: 18px;
            line-height: 27px;
            padding: 0 24px;
        }

        .banner-desc {
            height: initial;
            font-size: 14px;
            line-height: 21px;
            margin: 8px 16px 12px;
        }

        .banner-resource {
            height: 200px;

            .resource-container {
                // width: 361px;
                width: calc(100vw - 32px);
                height: 200px;

            }

            :deep(.swiper-slide) {
                // width: 361px;
                width: calc(100vw - 32px);
                transform: scale(0.84);
                transition: transform 0.3s ease;

                &.swiper-slide-active,
                &.swiper-slide-duplicate-active {
                    transform: scale(1);
                }

                &.swiper-slide-next,
                &.swiper-slide-duplicate-next {
                    position: relative;
                    transform: scaleY(0.88) translateX(-16px);
                }
            }

            .swiper-btn {
                display: none;
                pointer-events: none;
            }
        }

        .banner-resource-swiper-tab {
            align-self: flex-start;
            padding: 12px 16px 0 16px;
            margin: 0 0 16px 0;
            overflow-x: auto;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            width: 100%;
            border-bottom: none;

            &::-webkit-scrollbar {
                display: none;
            }

            .tab-item {
                flex-shrink: 0;
            }

            .tab-item {
                width: auto;
                display: flex;
                align-items: center;
                justify-content: center;
                white-space: nowrap;
                height: 40px;
                font-size: 14px;
                line-height: 21px;
                margin-right: 0;
                padding: 10px 18px;
                border-bottom: 1px solid #D8D8D8;

                &:nth-child(1) {
                    padding-left: 10px;
                }

                &:nth-last-child(1) {
                    padding-right: 10px;
                }

                &.active {
                    line-height: 21px;
                    border-bottom: 1px solid #DF0428;
                }
            }
        }

        .banner-sub-desc {
            font-size: 12px;
            line-height: 18px;
            margin: 0 16px;
        }

    }
}

// .banner-resource {
//     --swiper-pagination-bullet-opacity: 1;
//     --swiper-pagination-bullet-inactive-opacity: 1;

//     .swiper-pagination :deep(.swiper-pagination-bullet) {
//         width: 17px;
//         height: 2px;
//         border-radius: 2px;
//         transition: all .3s ease-in;
//         background-color: rgba(255, 255, 255, 0.5);
//     }

//     .swiper-pagination :deep(.swiper-pagination-bullet-active) {
//         background-color: #fff;
//     }
// }</style>