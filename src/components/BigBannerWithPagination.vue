<template>
    <div ref="bigBanner" class="big-banner big-banner-with-pagination">
        <div class="banner-title" :style="{ color: data.textColor }" v-if="data.title">{{ data.title }}</div>
        <div class="banner-desc" :style="{ color: data.textColor }" v-if="data.desc">
            {{ data.desc }}
        </div>
        <div class="banner-resource">
            <div ref="resourceContainer" class="swiper">
                <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="(item, index) in data.list" :key="index">
                        <div class="resource-container">
                            <div class="resource-title">{{ item.title }}</div>
                            <img class="resource" :src="$patchRawUrl(item.resource.url)" alt="" v-if="item.resource.type === 'image'">
                            <video class="resource video" :class="{ active: activeIndex == index }"
                                :src="$patchRawUrl(item.resource.url)" v-if="item.resource.type === 'video'" muted></video>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flx-jc-c">
            <div class="banner-swiper-pagination">
                <div class="swiper-pagination-prev-btn swiper-pagination-btn" :class="{ 'btn-disable': slideBeginning }"
                    @click="handleSlidePrev">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="18" viewBox="0 0 12 18" fill="none">
                        <path d="M9.8125 1.5L2.18955 9.12295L9.8125 16.5" stroke="#fff" stroke-width="3"
                            stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </div>
                <div ref="paginationElm" class="swiper-pagination"></div>
                <div class="swiper-pagination-next-btn swiper-pagination-btn" :class="{ 'btn-disable': slidEnd }"
                    @click="handleSlideNext">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="18" viewBox="0 0 12 18" fill="none">
                        <path d="M2.1875 16.5L9.81045 8.87705L2.1875 1.5" stroke="#fff" stroke-width="3"
                            stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang='ts'>
import { Pagination, Autoplay } from 'swiper';

const isMobile = ref(false);

onMounted(() => {
    isMobile.value = window.innerWidth < 768;
})

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: []
            }
        }
    }
})

const resourceContainer = ref<HTMLElement>();
const paginationElm = ref<HTMLElement>();

let swiperInstance = ref();

useSwiper(resourceContainer,
    {
        modules: [Pagination, Autoplay],
        lazy: {
            loadPrevNext: true,
        },
        speed: 500,
        // autoplay: {
        //     delay: 3000,
        //     disableOnInteraction: false,
        // },
        loop: true,
        slidesPerView: 'auto',
        loopedSlides: 2,
        initialSlide: 0,
        spaceBetween: 24,
        centeredSlides: true,
        pagination: {
            el: '.big-banner.big-banner-with-pagination .swiper-pagination',
            clickable: true,
        },
        on: {
            init: (swiper) => {
                swiperInstance.value = swiper;
            },
            slideChangeTransitionStart: (event) => {
                if (curVideo.value) {
                    curVideo.value.pause();
                }
            },
            slideChangeTransitionEnd: (event) => {
                activeIndex.value = event.realIndex;

                const container = event.el;
                const videoElement: HTMLVideoElement = container.querySelector('.swiper-slide-active video');
                if (videoElement) {
                    curVideo.value = videoElement;
                    videoElement.play().catch(() => { });
                }
            }
        }
    },
    {
        'paginationConfig': {
            el: paginationElm,
            clickable: true,
        },
    }
);

const curVideo = ref(null);
let activeIndex = ref(0);
let slideBeginning = ref(false);
let slidEnd = ref(false);

const handleSlideNext = () => {
    if (swiperInstance.value) {
        swiperInstance.value.slideNext();
    }
}
const handleSlidePrev = () => {
    if (swiperInstance.value) {
        swiperInstance.value.slidePrev();
    }
}

</script>
<style lang="less" scoped>
.big-banner {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    position: relative;
    margin-bottom: var(--component-mb);

    .banner-title {
        max-width: 816px;
        font-family: "PingFang SC";
        font-size: 48px;
        font-style: normal;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        text-align: center;
        color: var(--title-theme-color);

    }

    .banner-desc {
        max-width: 1260px;
        // height: 72px;
        flex-shrink: 0;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: var(--sub-title-font-weight);
        line-height: 36px;
        margin-top: 24px;
        margin-bottom: 40px;
        color: var(--sub-title-theme-color);
    }

    .banner-resource {
        width: 100%;
        height: 664px;

        .resource-container {
            display: flex;
            align-items: center;
            width: 1440px;
            height: 664px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;

            .resource-title {
                color: rgba(255, 255, 255, 0.97);
                font-family: "PingFang SC";
                font-size: 32px;
                font-weight: var(--title-font-weight);
                line-height: 48px;
                position: absolute;
                top: 40px;
                left: 40px;
            }

            .resource {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }


        }

        :deep(.swiper-slide) {
            width: 1440px;
            // transform: scale(0.88);
            // transition: all 0.3s ease;

            // &.swiper-slide-active  {
            //     transform: scale(1);
            // }
        }
    }


    .banner-swiper-pagination {
        display: flex;
        padding: 8px 12px;
        justify-content: center;
        align-items: center;
        border-radius: 34px;
        // border: 1px solid rgba(255, 255, 255, 0.50);
        // background: linear-gradient(180deg, #000 -42.86%, #555 100%);
        background:
            linear-gradient(180deg, #000 -42.86%, #555 100%) padding-box,
            linear-gradient(10deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.00) 100%) border-box;
        border: 1px solid transparent;
        margin-top: 40px;
        position: relative;
        z-index: 1;

        &:hover {
            // background: linear-gradient(180deg, #000 -42.86%, #888 100%),
            //     linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0.00) 100%);
            // border: 2px solid transparent;
            background:
                linear-gradient(180deg, #000 -42.86%, #888 100%) padding-box,
                linear-gradient(-30deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.00) 100%) border-box;
            border: 1px solid transparent;
        }

        .swiper-pagination-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            cursor: pointer;
            flex: 0 0 auto;
            transition: all 0.3s ease;

            &.btn-disable {
                opacity: 0.3;
                cursor: initial;
            }

            &.swiper-pagination-prev-btn {
                margin-right: 20px;
            }

            &.swiper-pagination-next-btn {
                margin-left: 20px;
            }
        }
    }


}

.flx-jc-c {
    display: flex;
    justify-content: center;
}

.sticky-container {
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    position: sticky;
    top: -20vh;
    width: 100%;
    overflow: hidden;

    .scale-overflow-hidden {
        display: flex;
        flex-direction: column;
        align-items: center;
        overflow: hidden;
    }
}

@media (max-width: 768px) {
    .big-banner {
        // margin-bottom: 40px;
        overflow: hidden;

        .banner-title {
            font-size: 18px;
            line-height: 27px;
            padding: 0 24px;
        }


        .banner-desc {
            height: initial;
            font-size: 14px;
            line-height: 21px;
            margin: 8px 16px 12px;
        }

        .banner-resource {
            height: initial;



            .resource-container {
                width: calc(100vw - 32px);
                aspect-ratio: 361/200;
                height: initial;
                border-radius: 6px;

                .resource-title {
                    font-size: 16px;
                    line-height: 21px;
                    top: 12px;
                    left: 12px;
                }
            }

            :deep(.swiper-slide) {
                width: calc(100vw - 32px);
            }


        }

        .banner-swiper-pagination {
            width: initial;
            height: 40px;
            align-self: flex-start;
            padding: 0 12px;
            margin: 12px 0 0;
            overflow-x: auto;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            border-bottom: none;

            .swiper-pagination-btn.swiper-pagination-prev-btn {
                margin-right: 12px;
            }

            .swiper-pagination-btn.swiper-pagination-next-btn {
                margin-left: 12px;
            }


            &::-webkit-scrollbar {
                display: none;
            }

            .tab-item {
                flex-shrink: 0;
            }

            .tab-item {
                width: auto;
                display: flex;
                align-items: center;
                justify-content: center;
                white-space: nowrap;
                height: 40px;
                font-size: 14px;
                line-height: 21px;
                margin-right: 0;
                padding: 10px 18px;
                border-bottom: 1px solid #D8D8D8;

                &:nth-child(1) {
                    padding-left: 10px;
                }

                &:nth-last-child(1) {
                    padding-right: 10px;
                }

                &.active {
                    line-height: 21px;
                    border-bottom: 1px solid #DF0428;
                }
            }
        }

        .banner-sub-desc {
            font-size: 12px;
            line-height: 18px;
            margin: 0 16px;
        }

    }

    .hidden-mob {
        display: none;
    }
}

.big-banner {
    --swiper-pagination-bullet-opacity: 1;
    --swiper-pagination-bullet-inactive-opacity: 1;
    --swiper-pagination-bullet-horizontal-gap: 12px;

    .swiper-pagination {
        display: flex;
        align-items: center;
        position: initial;
        cursor: pointer;
    }

    .swiper-pagination :deep(.swiper-pagination-bullet) {
        width: 10px;
        height: 10px;
        border-radius: 10px;
        transition: all .3s ease-in;
        background-color: #666;
    }

    .swiper-pagination :deep(.swiper-pagination-bullet-active) {
        background-color: #fff;
    }
}
</style>