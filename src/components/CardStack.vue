<template>
    <div class="card-stack-container" ref="cardStackRef">
        <div class="header">
            <h2 class="title" :style="{ color: data.textColor }">{{ data.title }}</h2>
            <p class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</p>
        </div>
        <template v-if="isMobile">
            <div ref="banner" class="center-container swiper">
                <div class="swiper-wrapper">
                    <div v-for="(item, index) in data.cards" :key="index" class="card swiper-slide"
                        :slide-direction="slideDirection">
                        <div class="img-container1">
                            <img :src="$patchRawUrl(item.image.url)" alt="card" class="card-image" />
                            <div class="img-title">{{ item.title }}</div>
                        </div>
                    </div>
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </template>
        <template v-else>

            <div class="center-container">
                <div v-for="(item, index) in data.cards" :key="index" class="card"
                    :class="{ active: currentHover == index }" @click="handleMouseOver(index)"
                    :style="calculateStyle(index)">
                    <div class="img-container1">
                        <img :src="$patchRawUrl(item.image.url)" alt="card" class="card-image" />
                        <div class="img-title">{{ item.title }}</div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { isClient } from "@vueuse/core";
import { cumulativeSum } from "../utils";
import { Navigation, Pagination, Autoplay, Lazy } from 'swiper';
import { Swiper, SwiperOptions } from 'swiper';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
})

const cardStackRef = ref(null);
const currentHover = ref(0)
let slideToRightItvl = null;
const calculateStyle = (index) => {
    const zIndex = props.data.cards.length - Math.abs(index - currentHover.value)
    const scale = Math.pow(0.9, props.data.cards.length - zIndex);
    const translateX = 30 - cumulativeSum(parseInt(index)) * 10;
    return {
        zIndex: zIndex,
        // transform: `scale(${scale})`,
        // transform: `translateX(${translateX}px) scale(${scale})`,
        // left: 212 + 200 * index + 'px',
    }
}

const handleMouseOver = (index) => {
    if (slideToRightItvl) {
        return;
    }
    currentHover.value = index
}

const slideToRight = () => {
    currentHover.value = 0;
    slideToRightItvl = 0;
    setTimeout(() => {
        slideToRightItvl = setInterval(() => {
            if (currentHover.value >= props.data.cards.length - 1) {
                clearInterval(slideToRightItvl);
                slideToRightItvl = null;
                console.log(slideToRightItvl);
            } else {
                currentHover.value = currentHover.value + 1;
            }
        }, 50);
    }, 500);
}

// useIntersectionTrigger({
//     target: cardStackRef,
//     onIntersect: slideToRight,
//     threshold: 0.1
// });

const activeIndex = ref(0);
const banner = ref<HTMLDivElement>();
const slideDirection = ref('next');
const isMobile = ref(false);
if (isClient) {
    console.log('isClient');
    isMobile.value = window.innerWidth < 768;
}
const swiperInstance = useSwiper(banner, {
    modules: [Pagination, Autoplay, Lazy],
    // loop: true,
    virtualTranslate: true,
    speed: 500,
    autoplay: {
        delay: 3000000,
        disableOnInteraction: false,
    },
    // 新增懒加载配置
    lazy: {
        loadPrevNext: true,
        loadPrevNextAmount: 1,
        loadOnTransitionStart: true,
    },
    preloadImages: false, // 必须设置为false
    pagination: {
        el: '.center-container .swiper-pagination',
    },
    on: {
        slideChangeTransitionEnd: (swiper) => {
            activeIndex.value = swiper.realIndex;
        },
        slideNextTransitionStart: (swiper) => {
            slideDirection.value = 'next';
        },
        slidePrevTransitionStart: (swiper) => {
            slideDirection.value = 'prev';
        },
    },
    enabled: isMobile.value,
});



</script>

<style lang="less" scoped>
.card-stack-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
    background-color: #fff;
    padding: 0 20px 100px;
    margin-bottom: var(--component-mb);

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding-top: 100px;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            max-width: 1260px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .center-container {
        display: flex;
        position: relative;
        width: 100%;
        max-width: 1440px;
        cursor: auto;
        border-radius: 12px;
        overflow: hidden;
    }

    .card {
        width: 200px;
        height: 553px;
        position: relative;
        transition: all 1.5s cubic-bezier(0.19, 1, 0.22, 1);
        flex: 1 1 auto;
        flex-basis: 6%;

        .img-container1 {
            width: 100%;
            height: 100%;
            position: absolute;
            overflow: hidden;
        }

        .card-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 1.5s cubic-bezier(0.19, 1, 0.22, 1);
        }

        .img-title {
            width: 120px;
            color: rgba(255, 255, 255, 0.97);
            font-family: "PingFang SC";
            font-size: 24px;
            font-weight: 400;
            line-height: 36px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: absolute;
            left: 40px;
            bottom: 40px;
        }

        &.active {
            /* width: 412px; */
            z-index: 10 !important;
            transform: scale(1.0) !important;
            flex-basis: 65.34%;

            .img-title {
                width: 100%;
            }
        }
    }
}

@media (max-width: 768px) {
    .card-stack-container {
        padding: 0 0 32px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }

        .title {
            margin-bottom: 12px;
        }

        .sub-title {
            display: none;
        }

        .center-container {
            display: flex;
            flex-direction: column;
            height: 432px;
            position: relative;
            width: 100%;
            border-radius: 0;
            overflow: hidden;
        }


        .card {
            width: 100%;
            height: 100%;
            position: absolute;
            flex: 1 1 auto;
            flex-basis: 100%;
            clip-path: polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%);

            .img-container1 {
                width: 100%;
                height: 100%;
                position: absolute;
                overflow: hidden;
            }

            .img-title {
                font-size: 18px;
                white-space: nowrap;
                left: 16px;
                bottom: 24px;
            }

            &.active {
                flex-basis: 0%;
            }


            &.swiper-slide-prev[slide-direction="next"] {
                animation: slideUpOut 0.5s ease-in forwards;
                flex-basis: 100%;
            }

            &.swiper-slide-active {
                flex-basis: 100%;
                animation: slideUpIn 0.5s ease-in forwards;
                z-index: 2;

                .img-title {
                    width: 100%;
                }
            }

            &.swiper-slide-next[slide-direction="prev"] {
                flex-basis: 100%;
                animation: slideUpOut 0.5s ease-in forwards;
            }
        }

        @keyframes slideUpIn {
            0% {
                clip-path: polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%);
            }

            100% {
                clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
            }
        }

        @keyframes slideUpOut {
            0% {
                clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
            }

            100% {
                clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%);
            }
        }

        .swiper-pagination {
            height: 16px;
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;
        }

        .center-container {
            --swiper-pagination-bullet-opacity: 1;
            --swiper-pagination-bullet-inactive-opacity: 1;

            .swiper-pagination :deep(.swiper-pagination-bullet) {
                width: 17px;
                height: 2px;
                border-radius: 2px;
                transition: all .3s ease-in;
                background-color: rgba(255, 255, 255, 0.5);
            }

            .swiper-pagination :deep(.swiper-pagination-bullet-active) {
                background-color: #fff;
            }
        }
    }
}
</style>