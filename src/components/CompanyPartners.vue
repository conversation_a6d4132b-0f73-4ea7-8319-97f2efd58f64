<template>
    <div class="company-partners">
        <div class="title" :style="{ color: data.textColor }">{{data.title}}</div>
        <div class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</div>
        <!-- <div class="partners-container" :class="{ paused: isPaused }">
            <div class="partners">
                <div class="partner" v-for="(item, index) in data.list" :key="index">
                    <img class="img" :src="item.img" alt="">
                </div>
                <div class="partner" v-for="(item, index) in data.list" :key="index">
                    <img class="img" :src="item.img" alt="">
                </div>
            </div>
        </div> -->
        <AutoLoopScroll :speed="speed" :isScrolling="!isPaused">
            <div ref="partnerRefs" class="partner" v-for="(item, index) in data.list" :key="index">
                <img class="img" :src="patchRawUrl(item.resource.url)" alt="">
            </div>
        </AutoLoopScroll>
        <div class="mask" v-if="isMobile"></div>
        <div class="mask partner" v-else @mouseenter="pauseAnimation" @mouseleave="resumeAnimation"></div>
    </div>
</template>
<script setup lang='ts'>

const patchRawUrl = usePatchRawUrl();
const isMobile = ref(false);
const partnerRefs = ref([]);

onMounted(() => {
    isMobile.value = window.innerWidth < 768;
    // 在DOM更新后计算速度
    nextTick(() => {
        calculateSpeed();
    });
})

const props = defineProps({
    data: {
        type: Object,
        default: () => {
           return {
                speed: 200,
           }
        }
    }
})

const speed = ref(50); // 默认速度
const isPaused = ref(false);

// 计算动画速度，保持70px/s的速度
const calculateSpeed = () => {
    if (partnerRefs.value && partnerRefs.value.length > 0) {
        // 计算单个partner的宽度（包括margin）
        const partnerElement = partnerRefs.value[0];
        const computedStyle = window.getComputedStyle(partnerElement);
        const width = partnerElement.offsetWidth;
        const marginRight = parseInt(computedStyle.marginRight) || 0;
        const partnerWidth = width + marginRight;
        
        // 计算所有partner的总宽度
        const totalWidth = partnerWidth * partnerRefs.value.length;
        
        // 计算动画周期：总宽度/速度 = 时间（秒）
        // 由于AutoLoopScroll使用的是50%的移动距离，所以需要除以2
        const duration = totalWidth / (props.data.speed || 200) / 2;
        
        speed.value = duration;
    }
};

const pauseAnimation = () => {
    console.log(props.data);
    isPaused.value = true;
};

const resumeAnimation = () => {
    isPaused.value = false;
};

// 监听数据变化，重新计算速度
watch(() => props.data?.list, () => {
    nextTick(() => {
        calculateSpeed();
    });
}, { deep: true });

</script>
<style lang="less" scoped>
.company-partners {
    // display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    position: relative;
    margin-bottom: var(--component-mb);

    .title {
        max-width: 816px;
        color: rgba(0, 0, 0, 0.8);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 48px;
        font-style: normal;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        margin: 0 auto;
    }

    .sub-title {
        max-width: 1260px;
        color: rgba(0, 0, 0, 0.8);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: var(--sub-title-font-weight);
        line-height: 36px;
        margin-top: 24px;
        // margin-bottom: 40px;
        margin: 0 auto 40px;
    }

    .partners-container {
        display: flex;
        align-items: center;
        align-self: flex-start;
        animation: scroll-partners 24s linear infinite;
        /* 添加动画 */
        animation-play-state: var(--animation-state, running);
        /* 动态控制动画状态 */
    }

    .paused {
        --animation-state: paused;
        /* 鼠标悬停时暂停动画 */
    }

    @keyframes scroll-partners {
        0% {
            transform: translateX(0);
        }

        100% {
            transform: translateX(-50%);
        }
    }

    .partners {
        display: flex;
        align-items: center;
        flex-shrink: 0;
    }

    .partner {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 240px;
        height: 120px;
        margin-right: 60px;
        flex-shrink: 0;

        .img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
    }

    .mask {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background: linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0) 10%, rgba(255, 255, 255, 0) 90%, #fff 100%);
        // pointer-events: none;
    }

    @media (max-width: 768px) {
        .title {
            font-size: 24px;
            line-height: 34px;
            margin-bottom: 12px;
        }

        .sub-title {
            font-size: 14px;
            line-height: 21px;
            margin-top: 0;
            margin-bottom: 24px;
        }

        .partner {
            width: 110px;
            height: 55px;
            margin-right: 40px;
        }
    }
}
</style>