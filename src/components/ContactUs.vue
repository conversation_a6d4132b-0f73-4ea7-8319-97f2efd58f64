<template>
    <div class="contact-us-container">
        <h2 class="main-title" :style="{ color: data.textColor }">{{data.title}}</h2>
        <div class="content-wrapper">
            <div class="contact-info" :class="{ active: data.showContactInfo }">
                <h3 class="sub-title">{{data.title}}</h3>
                <p class="desc">了解更多产品信息或预约现场演示</p>
                <div class="contact-items">
                    <div class="contact-item">
                        <div class="item-label">
                            <div class="icon-bg"><i class="icon-phone label-icon"></i></div>电话咨询：
                        </div>
                        <div class="item-value">{{ data.phone }}</div>
                    </div>
                    <div class="contact-item">
                        <div class="item-label">
                            <div class="icon-bg"><i class="icon-mail label-icon"></i></div>邮箱咨询：
                        </div>
                        <div class="item-value">{{ data.email }}</div>
                    </div>
                    <div class="contact-item">
                        <div class="item-label">
                            <div class="icon-bg"><i class="icon-location label-icon"></i></div>公司地址：
                        </div>
                        <div class="item-value">{{data.addr}}</div>
                    </div>
                </div>
            </div>
            <!-- 右侧表单组件 -->
            <SubmitInfoForm 
                :data="submitFormData" 
                class="submit-form-container" 
                @submit="handleFormSubmit"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
const config = useRuntimeConfig();
const baseURL = config.public.NUXT_API_BASE;

// const props = defineProps({
//     data: {
//         type: Object,
//         default: () => {
//             return {
//                 showContactInfo: 1,
//                 submitFormData: {
//                     title: '咨询与反馈',
//                     type: 'download',
//                     targetUrl: '/test/01.pdf',
//                     productList:[]
//                 }
//             }
//         }
//     },

// });

const res: any = await useAsyncData(
    () => $fetch(baseURL + '/components/component-prop-data-by-name?name=ContactUs')
)

const data = res.data.value.data;

const submitFormData = ref({
    title: '咨询与反馈',
    type: 'download',
    privacyFile:data.privacyFile,
    usePolicy:data.usePolicy,
    productList:data.productList
})

// 处理表单提交事件
const handleFormSubmit = (formData) => {
    console.log('ContactUs表单提交:', formData);
    // 在这里可以添加任何需要在表单提交后执行的逻辑
}
</script>


<style lang="less" scoped>
.contact-us-container {
    max-width: 1440px;
    padding: 0;
    margin: 0 auto;
    margin-bottom: var(--component-mb);

    .main-title {
        max-width: 816px;
        color: rgba(0, 0, 0, 0.8);
        font-family: "PingFang SC";
        font-size: 48px;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        text-align: center;
        margin: 0 auto 40px;
    }

    .content-wrapper {
        display: flex;
        justify-content: center;
        gap: 24px;
    }

    .contact-info {
        width: 708px;
        min-width: 400px;
        // height: 576px;
        min-height: 576px;
        padding: 32px;
        background: #fff;
        border-radius: 12px;
        // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        background: linear-gradient(134deg, #888888 -83.52%, #000000 97.69%);

        .sub-title {
            color: rgba(255, 255, 255, 0.97);
            font-family: "PingFang SC";
            font-size: 32px;
            font-weight: var(--title-font-weight);
            line-height: 48px;
            margin-bottom: 12px;
        }

        .desc {
            color: rgba(255, 255, 255, 0.57);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            margin-bottom: 40px;
        }

        .contact-items {
            display: flex;
            flex-direction: column;
            gap: 32px;

            .contact-item {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .item-label {
                    display: flex;
                    align-items: center;
                    gap: 24px;
                    color: rgba(255, 255, 255, 0.57);
                    font-family: "PingFang SC";
                    font-size: 18px;
                    font-weight: var(--title-font-weight);
                    line-height: 27px;
                    .icon-bg{
                        line-height: 0;
                    }
                }

                .item-value {
                    color: rgba(255, 255, 255, 0.91);
                    font-family: "PingFang SC";
                    font-size: 18px;
                    font-weight: 400;
                    line-height: 27px;
                    margin-left: 44px;
                }
            }
        }
    }

    .submit-form-container {
        flex: 1;
        max-width: 708px;
    }
}

@media (max-width: 768px) {
    .contact-us-container {
        margin-bottom: 0;

        .content-wrapper {
            flex-direction: column;
        }

        .main-title {
            font-size: 18px;
            line-height: 27px;
            margin-bottom: 12px;
        }

        .contact-info {
            display: none;
            padding: 32px 12px;
            height: 307px;
            min-height: initial;
            min-width: initial;

            .sub-title,
            .desc {
                display: none;
            }

            &.active {
                display: block;
                width: initial;
                border-radius: 6px;
                margin: 0 16px;
            }

            .contact-items .contact-item {
                gap: 0;

                .item-label {
                    gap: 12px;
                    font-size: 14px;
                    line-height: 21px;
                    align-items: flex-start;

                    .icon-bg {
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        width: 32px;
                        height: 32px;
                        border-radius: 32px;
                        background: rgba(255, 255, 255, 0.20);
                        line-height: initial;

                        .label-icon {
                            width: 16px;
                            height: 16px;
                        }

                    }
                }

                .item-value {
                    font-size: 14px;
                    line-height: 21px;
                    margin-top: -7px;
                }
            }

        }

        .submit-form-container {

            :deep(.submit-form) {
                background-color: transparent;

                .form-grid .input-container .input[data-v-36f2ccbf] {
                    background: transparent;
                }

                .form-textarea textarea {
                    background: transparent;
                }

                .form-footer .submit-btn {
                    border-radius: 20px;
                }
            }
        }
    }
}
</style>