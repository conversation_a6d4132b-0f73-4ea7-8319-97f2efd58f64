<template>
  <div class="core-advantages">
    <div class="header">
      <h2 class="title" :style="{ color: data.textColor }">{{ data.title }}</h2>
    </div>

    <div class="card-container top-cards" v-if="data.topCards.length > 0">
      <div class="card-item" v-for="(item, index) in data.topCards" :key="index">
        <div class="item-left">
          <div class="card-title">{{ item.title }}</div>
          <div class="card-content">{{ item.desc }}</div>
        </div>
        <img :src="$patchRawUrl(item.img.url)" alt="卡片图标" class="item-right card-icon" />
      </div>
    </div>

    <div class="card-container bottom-cards">
      <div class="card-item" v-for="(item, index) in data.bottomCards" :key="index + 3">
        <div class="card-title">
          <i class="card-icon"></i>
          <span>{{ item.title }}</span>
        </div>
        <div class="card-content">{{ item.desc }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        title: '',
        desc: '',
        topCards: [],
        bottomCards: []
      }
    }
  }
})

</script>

<style scoped lang="less">
.core-advantages {
  width: 100%;
  padding: 0 20px;
  margin-bottom: var(--component-mb);

  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 40px;

    .title {
      max-width: 816px;
      text-align: center;
      color: rgba(0, 0, 0, 0.8);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 48px;
      font-style: normal;
      font-weight: var(--title-font-weight);
      line-height: 72px;
    }
  }

  .card-container {

    &.top-cards {
      display: flex;
      // flex-wrap: wrap;
      gap: 24px;
      min-width: 1200px;
      max-width: 1440px;
      margin: 0 auto 40px;

      .card-item {
        display: flex;
        max-width: 464px;
        aspect-ratio: 464/200;
        border-radius: 12px;
        background: #FFF;
        border-radius: 12px;

        .item-left {
          margin-top: 32px;
          margin-left: 32px;

          .card-title {
            color: rgba(0, 0, 0, 0.80);
            font-family: "PingFang SC";
            font-size: 28px;
            font-weight: var(--title-font-weight);
            line-height: 42px;
            margin-bottom: 12px;
          }

          .card-content {
            color: rgba(0, 0, 0, 0.65);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
          }
        }

        .item-right {
          width: 120px;
          aspect-ratio: 1/1;
          margin: 56px 24px 24px;
          object-fit: contain;
        }
      }
    }

    &.bottom-cards {
      display: flex;
      gap: 29px;
      max-width: 1440px;
      // min-width: 1336px;
      grid-template-columns: repeat(4, 1fr);
      margin: 0 auto;

      .card-item {
        max-width: 337px;
        aspect-ratio: 337/172;
        border-radius: 12px;
        background: #FFF;
        border-radius: 12px;


        .card-title {
          display: flex;
          align-items: center;
          color: rgba(0, 0, 0, 0.80);
          font-family: "PingFang SC";
          font-size: 28px;
          font-weight: var(--title-font-weight);
          line-height: 42px;
          margin: 32px 32px 12px;

          .card-icon {
            display: inline-block;
            width: 32px;
            height: 32px;
            margin-right: 12px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: 100%;
          }
        }

        .card-content {
          color: rgba(0, 0, 0, 0.65);
          font-family: "PingFang SC";
          font-size: 18px;
          font-weight: 400;
          line-height: 27px;
          margin: 0 32px 12px 76px;
        }

        &:nth-child(1) {
          filter: drop-shadow(-5px 0 0 #0C95F3);

          .card-title .card-icon {
            background-image: url("../../assets/imgs/icon_hyyy_blue.png");
          }
        }

        &:nth-child(2) {
          filter: drop-shadow(-5px 0 0 #FF6B35);

          .card-title .card-icon {
            background-image: url("../../assets/imgs/icon_hyyy_orange.png");
          }
        }

        &:nth-child(3) {
          filter: drop-shadow(-5px 0 0 #00FC3B);

          .card-title .card-icon {
            background-image: url("../../assets/imgs/icon_hyyy_green.png");
          }
        }

        &:nth-child(4) {
          filter: drop-shadow(-5px 0 0 #7C50FF);

          .card-title .card-icon {
            background-image: url("../../assets/imgs/icon_hyyy_purple.png");
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .core-advantages {
    padding: 0 16px;

    .header {
      text-align: center;
      margin-bottom: 12px;

      .title {
        font-size: 18px;
        line-height: 21px;
      }
    }

    .card-container {

      &.top-cards {
        flex-direction: column;
        min-width: initial;
        gap: 16px;
        margin-bottom: 24px;

        .card-item {
          width: 100%;
          max-width: initial;
          aspect-ratio: initial;
          border-radius: 6px;

          .item-left {
            margin: 12px;
          }

          .item-left .card-title {
            font-size: 14px;
            line-height: 27px;
            margin-bottom: 4px;
          }

          .item-left .card-content {
            font-size: 12px;
            line-height: 18px;
          }

          .item-right {
            width: 40px;
            height: 40px;
            margin: auto 12px 12px auto;
          }
        }
      }

      &.bottom-cards {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin: 0 auto;

        .card-item {
          max-width: initial;
          aspect-ratio: initial;
          border-radius: 6px;
          background: #FFF;


          .card-title {
            font-size: 14px;
            line-height: 21px;
            margin: 12px 12px 4px;

            .card-icon {
              width: 16px;
              height: 16px;
              margin-right: 8px;
            }
          }

          .card-content {
            font-size: 12px;
            line-height: 18px;
            margin: 0 12px 12px;
          }

          &:nth-child(1) {
            filter: initial;
          }

          &:nth-child(2) {
            filter: initial;
          }

          &:nth-child(3) {
            filter: initial;
          }

          &:nth-child(4) {
            filter: initial;
          }
        }
      }
    }
  }
}
</style>