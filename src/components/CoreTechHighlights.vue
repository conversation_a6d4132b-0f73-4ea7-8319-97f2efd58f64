<template>
    <div class="core-tech-highlights">
        <div class="header">
            <h2 class="title" :style="{ color: data.textColor }">{{ data.title }}</h2>
            <p class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</p>
        </div>

        <div class="tab-bar">
            <div class="tab-item" v-for="(tab, index) in data.list" :key="index" @click="activeTab = index"
                :class="{ 'active': activeTab === index }">
                {{ tab.name }}
            </div>
        </div>

        <div class="tab-content">
            <div class="content-left">
                <img :src="patchRawUrl(curContent.img.url)" alt="" class="left-img">
            </div>
            <div class="content-right">
                <div class="content-title">{{ curContent.title }}</div>
                <div class="content-desc">
                    {{ curContent.desc }}
                </div>
                <div class="content-feature">
                    <div class="feature-item" v-for="(item, index) in curContent.feature" :key="index">
                        <img :src="patchRawUrl(item.icon.url)" alt="" class="feature-icon" v-if="item.icon">
                        <div class="feature-desc">
                            <div class="feature-title">
                                <span>{{ item.name }}</span>
                            </div>
                            <div class="feature-desc">
                                {{ item.desc }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const patchRawUrl = usePatchRawUrl();

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                title: '',
                desc: '',
                topCards: [],
                bottomCards: []
            }
        }
    }
})



const activeTab = ref(0);

const curContent = computed(() => {
    return props.data.list[activeTab.value];
});

</script>

<style scoped lang="less">
.core-tech-highlights {
    width: 100%;
    padding: 0 20px 100px;
    margin-bottom: var(--component-mb);
    background-color: #fff;

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding-top: 100px;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            max-width: 1260px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .tab-bar {
        max-width: 1440px;
        margin: 0 auto 40px;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #D8D8D8;
        gap: 24px;

        .tab-item {
            display: inline-flex;
            height: 56px;
            padding: 0 24px;
            justify-content: center;
            align-items: center;
            color: rgba(0, 0, 0, 0.80);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 18px;
            font-weight: var(--sub-title-font-weight);
            line-height: 27px;
            cursor: pointer;
            position: relative;
            white-space: nowrap;

            &.active {
                color: #DF0428;
                font-weight: var(--title-font-weight);

                &::after {
                    content: '';
                    display: block;
                    width: 100%;
                    height: 1px;
                    background: #DF0428;
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                }
            }
        }
    }

    .tab-content {
        max-width: 1440px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        margin: 0 auto;

        .content-left {
            .left-img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                aspect-ratio: 708/410;
                border-radius: 12px;
            }
        }

        .content-right {
            display: flex;
            flex-direction: column;

            .content-title {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 32px;
                font-weight: var(--title-font-weight);
                line-height: 48px;
                margin-bottom: 12px;
            }

            .content-desc {
                color: rgba(0, 0, 0, 0.65);
                font-family: "PingFang SC";
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                margin-bottom: 12px;
            }

            .content-feature {
                display: flex;
                flex-direction: column;
                margin-top: auto;
                gap: 24px;

                .feature-item {
                    display: flex;
                    position: relative;
                    padding-left: 88px;

                    .feature-icon {
                        width: 64px;
                        height: 64px;
                        position: absolute;
                        left: 0;
                    }

                    .feature-title {
                        color: #000;
                        font-family: "PingFang SC";
                        font-size: 18px;
                        font-weight: var(--title-font-weight);
                        line-height: 27px;
                        margin-bottom: 8px;
                    }

                    .feature-desc {
                        color: #000;
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-weight: 300;
                        line-height: 21px;
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .core-tech-highlights {
        padding: 0 16px 32px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }

        .tab-bar {
            margin: 0 auto 12px;
            border-bottom: none;
            gap: 16px;

            .tab-item {
                height: 40px;
                padding: 0 10px;
                font-size: 14px;
                line-height: 21px;
            }
        }

        .tab-content {
            grid-template-columns: repeat(1, 1fr);
            gap: 16px;

            .content-left {
                .left-img {
                    border-radius: 6px;
                }
            }

            .content-right {
                display: flex;
                flex-direction: column;

                .content-title {
                    font-size: 14px;
                    line-height: 21px;
                    margin-bottom: 4px;
                }

                .content-desc {
                    font-size: 12px;
                    line-height: 18px;
                    margin-bottom: 16px;
                }

                .content-feature {
                    gap: 24px;

                    .feature-item {
                        height: initial;
                        padding-left: 48px;

                        .feature-icon {
                            width: 40px;
                            height: 40px;
                        }

                        .feature-title {
                            font-size: 12px;
                            line-height: 18px;
                            margin-bottom: 4px;
                        }

                        .feature-desc {
                            font-size: 12px;
                            line-height: 18px;
                        }
                    }
                }
            }
        }
    }
}
</style>