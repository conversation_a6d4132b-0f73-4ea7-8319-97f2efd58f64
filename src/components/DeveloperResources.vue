<template>
    <div class="developer-resources">
        <div class="header">
            <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
        </div>
        <div class="resource-cards">
            <div class="resource-card" v-for="item in data.list" :key="item.title">
                <div class="card-icon">
                    <img :src="$patchRawUrl(item.icon.url)" />
                </div>
                <div class="card-info">
                    <h3 class="card-title">{{ item.title }}</h3>
                    <p class="card-desc">{{ item.desc }}</p>
                    <a class="jump-more-btn" :href="item.jumpLink">
                        <span>了解更多</span>
                        <i class="icon-right-arrow"></i>
                    </a>
                </div>
                <a class="jump-link-mask" :href="item.jumpLink"></a>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {

            }
        }
    }
})

</script>

<style scoped lang="less">
.developer-resources {
    width: 100%;
    padding: 0 20px 100px;
    background: #FFFFFF;
    margin-bottom: var(--component-mb);

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding-top: 100px;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .resource-cards {
        display: flex;
        justify-content: center;
        margin-top: 40px;
        gap: 24px;

        .resource-card {
            width: 464px;
            min-height: 112px;
            background: #FFFFFF;
            border-radius: 12px;
            padding: 24px;
            display: flex;
            align-items: center;
            cursor: pointer;
            position: relative;

            &:hover {
                box-shadow: 0px 2px 8px 1px rgba(0, 0, 0, 0.05), 0px 4px 20px 0px rgba(0, 0, 0, 0.05), 0px 1px 15px 0px rgba(0, 0, 0, 0.05);
            }

            .card-icon {
                flex: 0 0 auto;
                width: 64px;
                height: 64px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .card-info {
                .card-title {
                    font-family: PingFang SC;
                    font-size: 18px;
                    font-weight: 400;
                    color: #000000;
                    line-height: 1.5;
                    margin-bottom: 12px;
                }

                .card-desc {
                    font-family: PingFang SC;
                    font-size: 14px;
                    font-weight: 400;
                    color: #000000;
                    line-height: 1.5;
                }

                .jump-more-btn {
                    display: none;
                }
            }

            .jump-link-mask {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
            }
        }
    }
}

@media (max-width: 768px) {
    .developer-resources {
        padding: 0 16px 32px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }

        .resource-cards {
            flex-direction: column;
            margin-top: 12px;
            gap: 16px;

            .resource-card {
                width: 100%;
                align-items: flex-start;
                background: #F5F5F5;
                border-radius: 6px;
                padding: 12px;

                &:hover {
                    box-shadow: none;
                }

                .card-icon {
                    flex: 0 0 auto;
                    width: 40px;
                    height: 40px;
                    margin-right: 16px;
                }

                .card-info {
                    .card-title {
                        font-size: 14px;
                        font-weight: var(--title-font-weight);
                        line-height: 1.5;
                        margin-bottom: 8px;
                    }

                    .card-desc {
                        font-size: 14px;
                        line-height: 1.5;
                        margin-bottom: 8px;
                    }

                    .jump-more-btn {
                        display: flex;
                        align-items: center;
                        justify-self: flex-start;
                        color: #1677FF;
                        font-family: "PingFang SC";
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 40px;
                        cursor: pointer;

                        .icon-right-arrow {
                            background-color: #1677FF;
                            margin-left: 8px;
                            transition: all 0.3s ease-in-out;
                        }
                    }
                }
            }
        }
    }
}
</style>