<template>
    <div class="external-video-gallery">
        <div class="header">
            <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
            <p class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</p>
        </div>

        <div class="video-grid">
            <div v-for="(video, index) in data.videos" :key="index" class="video-card" @click="openModal(video.url)">
                <div class="thumbnail-container">
                    <img :src="$patchRawUrl(video.thumbnail.url)" alt="视频预览" class="thumbnail" />
                </div>
                <div class="more-video" v-if="index == data.videos.length - 1 && !isMobile"
                    @click.stop="toVideoHome(data.videoHome)">
                    <span class="more-text">查看更多场景</span>
                    <div class="more-btn">
                        <i class="icon-right-arrow"></i>
                    </div>
                </div>
                <div class="card-desc" v-if="index < data.videos.length - 1 || isMobile">
                    <span class="video-title">{{ video.title }}</span>
                    <div class="controls">
                        <span class="watch-text">观看视频</span>
                        <div class="open-btn" @click.stop="openModal(video.url)">
                            <i class="icon-play-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="open-more-btn" @click.stop="toVideoHome($patchRawUrl(data.videoHome))">查看更多场景</div>

        <video-modal :videoUrl="currentVideoUrl" v-if="showModal" @close="closeModal"></video-modal>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: []
            }
        }
    }
})

const showModal = ref(false)
const currentVideoUrl = ref('');
const isMobile = ref(false);

const toVideoHome = (homeUrl) => {
    const url = homeUrl || 'https://space.bilibili.com/484123810?spm_id_from=333.337.0.0';
    window.open(url, '_blank');
}

const openModal = (videoUrl: any) => {
    currentVideoUrl.value = videoUrl
    showModal.value = true
    document.body.style.overflow = 'hidden'
}

const closeModal = () => {
    showModal.value = false
    document.body.style.overflow = 'auto'
}

onMounted(() => {
    isMobile.value = window.innerWidth < 768;
})
</script>

<style lang="less" scoped>
.external-video-gallery {
    width: 100%;
    background: #fff;
    padding: 0 20px 100px;
    margin-bottom: var(--component-mb);

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding-top: 100px;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            max-width: 1260px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .video-grid {
        display: flex;
        justify-content: center;
        gap: 24px;
        max-width: 1440px;
        margin: 0 auto;

        .video-card {
            width: 342px;
            // height: 440px;
            aspect-ratio: 342/440;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0px 6px 30px 5px rgba(0, 0, 0, 0.05), 0px 16px 24px 2px rgba(0, 0, 0, 0.04), 0px 8px 10px -5px rgba(0, 0, 0, 0.08);
            cursor: pointer;
            position: relative;

            .thumbnail-container {
                width: 100%;
                height: 100%;
                overflow: hidden;

                .thumbnail {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .card-desc {
                padding: 24px 24px 29px;
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;

                .video-title {
                    display: flex;
                    color: rgba(255, 255, 255, 0.91);
                    font-family: "PingFang SC";
                    font-size: 18px;
                    font-weight: var(--sub-title-font-weight);
                    line-height: 27px;
                    margin-bottom: 22px;
                }

                .controls {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    height: 21px;

                    .watch-text {
                        color: rgba(255, 255, 255, 0.91);
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 21px;
                    }

                    .open-btn {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 56px;
                        height: 56px;
                        border-radius: 56px;
                        background-color: rgba(0, 0, 0, 0.2);
                        padding-left: 2px;
                    }
                }


            }

            .more-video {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0%;
                left: 0%;

                .more-text {
                    color: rgba(255, 255, 255, 0.91);
                    text-align: center;
                    font-family: "PingFang SC";
                    font-size: 24px;
                    font-weight: 400;
                    line-height: 36px;
                    padding: 0 12px;
                    margin-top: -6px;
                    margin-bottom: 24px;
                }

                .more-btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 56px;
                    height: 56px;
                    border-radius: 56px;
                    border: 1px solid #fff;

                    &:hover {
                        background: rgba(255, 255, 255, 0.2);
                    }
                }
            }
        }
    }

    .open-more-btn {
        display: none;
    }
}

@media(max-width: 768px) {
    .external-video-gallery {
        padding: 0 16px 32px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }

        .video-grid {
            flex-wrap: wrap;
            gap: 16px;
            justify-content: space-between;

            .video-card {
                width: calc(50% - 8px);
                aspect-ratio: 170/160;
                border-radius: 6px;

                .card-desc {
                    display: flex;
                    justify-content: space-between;
                    padding: 8px;

                    .video-title {
                        font-size: 14px;
                        line-height: 20px;
                        margin-bottom: 0;
                        margin-right: 8px;
                        overflow: hidden;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .controls {
                        height: 40px;

                        .watch-text {
                            display: none;
                        }

                        .open-btn {
                            width: 40px;
                            height: 40px;
                            border-radius: 40px;
                        }
                    }
                }

            }
        }

        .open-more-btn {
            display: flex;
            width: 100%;
            height: 40px;
            padding: 12px 32px;
            justify-content: center;
            align-items: center;
            border-radius: 20px;
            background: linear-gradient(180deg, #000 -42.86%, #555 100%);
            color: rgba(255, 255, 255, 0.97);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            text-align: center;
            margin: 24px auto 0;
        }
    }
}
</style>