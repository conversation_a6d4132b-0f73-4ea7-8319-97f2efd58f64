<template>
    <div class="external-video-gallery">
        <div class="header">
            <h2 class="title" :style="{ color: data.textColor }">{{ data.title }}</h2>
            <p class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</p>
        </div>

        <div class="video-grid">
            <div v-for="(video, index) in data.videos" :key="index" class="video-card" @click="openModal(video)">
                <div class="thumbnail-container">
                    <img :src="video.thumbnail" alt="视频预览" class="thumbnail" />
                </div>
                <div class="card-desc" v-if="index < data.videos.length - 1 || isMobile">
                    <div class="video-title">{{ video.title }}</div>
                    <div class="video-desc-text">{{ video.desc }}</div>
                    <div class="data-percent">
                        <div class="data-item" v-for="(item, index) in video.percentData" :key="index">
                            <span class="data-value">{{ item.value }}</span>
                            <span class="data-key">{{ item.key }}</span>
                        </div>
                    </div>
                    <div class="controls">
                        <div class="open-btn" @click.stop="openModal(video)">
                            <i class="icon-play-triangle"></i>
                        </div>
                    </div>
                </div>
                <div class="more-video" v-if="index == data.videos.length - 1 && !isMobile" @click.stop="toVideoHome">
                    <span class="more-text">查看更多场景</span>
                    <div class="more-btn">
                        <i class="icon-right-arrow"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="open-more-btn" @click.stop="toVideoHome">查看更多场景</div>

        <video-modal :videoUrl="currentVideoUrl" v-if="showModal" @close="closeModal"></video-modal>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: []
            }
        }
    }
})

const showModal = ref(false)
const currentVideoUrl = ref('');
const isMobile = ref(false);

const toVideoHome = () => {
    const url = props.data.videoHome || 'https://space.bilibili.com/484123810?spm_id_from=333.337.0.0';
    window.open(url, '_blank');
}

const openModal = (video: any) => {
    currentVideoUrl.value = video.url
    showModal.value = true
    document.body.style.overflow = 'hidden'
}

const closeModal = () => {
    showModal.value = false
    document.body.style.overflow = 'auto'
}

onMounted(() => {
    isMobile.value = window.innerWidth < 768;
})
</script>

<style lang="less" scoped>
.external-video-gallery {
    width: 100%;
    background: #fff;
    padding: 0 20px 100px;
    margin-bottom: var(--component-mb);

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding-top: 100px;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            max-width: 1260px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .video-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        max-width: 1440px;
        margin: 0 auto;

        .video-card {
            display: flex;
            max-width: 708px;
            max-height: 380px;
            aspect-ratio: 708/380;
            border-radius: 12px;
            overflow: hidden;
            background-color: #f5f5f5;
            cursor: pointer;
            position: relative;

            .thumbnail-container {
                max-width: 50%;
                height: 100%;
                aspect-ratio: 342/380;
                overflow: hidden;
                flex: 0 0 auto;

                .thumbnail {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            &:nth-last-child(1) {
                .thumbnail-container {
                    width: 100%;
                    max-width: initial;
                }
            }

            .card-desc {
                width: 100%;
                padding: 24px;

                .video-title {
                    display: flex;
                    color: rgba(0, 0, 0, 0.8);
                    font-family: "PingFang SC";
                    font-size: 18px;
                    font-weight: var(--title-font-weight);
                    line-height: 27px;
                    margin-bottom: 4px;
                }

                .video-desc-text {
                    color: rgba(0, 0, 0, 0.65);
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-weight: var(--sub-title-font-weight);
                    line-height: 21px;
                    margin-bottom: 42px;
                }

                .data-percent {
                    display: flex;
                    justify-content: space-between;

                    .data-item {
                        display: flex;
                        flex-direction: column;


                        .data-key {
                            color: rgba(0, 0, 0, 0.80);
                            font-family: "PingFang SC";
                            font-size: 14px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 21px;
                            margin-bottom: 4px;
                        }

                        .data-value {
                            font-family: PingFang SC;
                            font-size: 32px;
                            font-style: normal;
                            font-weight: 700;
                            line-height: 48px;
                            background: linear-gradient(180deg, #8BBBFF 0%, var(--link-, #1677FF) 100%);
                            background-clip: text;
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }
                    }
                }

                .controls {
                    position: absolute;
                    right: 24px;
                    bottom: 24px;

                    .open-btn {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 56px;
                        height: 56px;
                        border-radius: 56px;
                        background-color: rgba(0, 0, 0, 0.2);
                        padding-left: 2px;
                    }
                }


            }

            .more-video {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0%;
                left: 0%;

                .more-text {
                    color: rgba(255, 255, 255, 0.91);
                    text-align: center;
                    font-family: "PingFang SC";
                    font-size: 24px;
                    font-weight: 400;
                    line-height: 36px;
                    padding: 0 12px;
                    margin-top: -6px;
                    margin-bottom: 24px;
                }

                .more-btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 56px;
                    height: 56px;
                    border-radius: 56px;
                    border: 1px solid #fff;

                    &:hover {
                        background: rgba(255, 255, 255, 0.2);
                    }
                }
            }
        }
    }

    .open-more-btn {
        display: none;
    }
}

@media(max-width: 768px) {
    .external-video-gallery {
        padding: 0 16px 32px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 16px;
            max-width: 100%;
            margin: 0 auto;

            .video-card {
                display: flex;
                flex-direction: column;
                max-width: initial;
                max-height: initial;
                aspect-ratio: initial;
                border-radius: 6px;
                overflow: hidden;
                background-color: #f5f5f5;
                cursor: pointer;
                position: relative;

                .thumbnail-container {
                    max-width: initial;
                    ;
                    height: initial;
                    aspect-ratio: 361/160;
                    overflow: hidden;
                    flex: 0 0 auto;

                    .thumbnail {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                &:nth-last-child(1) {
                    .thumbnail-container {
                        width: 100%;
                        max-width: initial;
                    }
                }

                .card-desc {
                    width: 100%;
                    padding: 12px 12px 24px;

                    .video-title {
                        font-size: 14px;
                        line-height: 21px;
                        margin-bottom: 8px;
                    }

                    .video-desc-text {
                        font-size: 12px;
                        line-height: 18px;
                        margin-bottom: 12px;
                    }

                    .data-percent {
                        justify-content: flex-start;

                        .data-item {
                            display: flex;
                            flex-direction: column;
                            margin-right: 24px;

                            .data-key {
                                font-size: 14px;
                                line-height: 21px;
                                margin-bottom: 4px;
                            }

                            .data-value {
                                font-size: 18px;
                                line-height: 27px;
                            }
                        }
                    }

                    .controls {
                        right: 12px;
                        bottom: 24px;

                        .open-btn {
                            width: 40px;
                            height: 40px;
                            border-radius: 40px;
                            padding-left: 2px;
                        }
                    }


                }

                .more-video {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0%;
                    left: 0%;

                    .more-text {
                        color: rgba(255, 255, 255, 0.91);
                        text-align: center;
                        font-family: "PingFang SC";
                        font-size: 24px;
                        font-weight: 400;
                        line-height: 36px;
                        padding: 0 12px;
                        margin-top: -6px;
                        margin-bottom: 24px;
                    }

                    .more-btn {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 56px;
                        height: 56px;
                        border-radius: 56px;
                        border: 1px solid #fff;

                        &:hover {
                            background: rgba(255, 255, 255, 0.2);
                        }
                    }
                }
            }
        }

        .open-more-btn {
            display: flex;
            width: 100%;
            height: 40px;
            padding: 12px 32px;
            justify-content: center;
            align-items: center;
            border-radius: 20px;
            background: linear-gradient(180deg, #000 -42.86%, #555 100%);
            color: rgba(255, 255, 255, 0.97);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            text-align: center;
            margin: 24px auto 0;
        }

    }
}
</style>