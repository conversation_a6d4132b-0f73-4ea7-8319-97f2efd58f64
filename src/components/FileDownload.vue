<template>
    <div class="file-download">
        <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
        <div class="tab-bar">
            <div class="tab-item" v-for="(tab, index) in tabs" :key="index" @click="activeTab = index"
                :class="{ 'active': activeTab === index }">
                {{ tab.name }}
            </div>
        </div>
        <div class="file-items">
            <div class="file-item" v-for="(item, index) in curFiles" :key="index">
                <i class="file-type-icon icon-doc" :class="{ 'icon-model': item.type == 'model' }"></i>
                <div class="file-name">{{ item.name }}</div>
                <div class="download-btn" @click="handleDownload(item)">
                    <span class="download-btn-link">下载</span>
                    <i class="icon-download"></i>
                </div>
            </div>
        </div>

        <!-- 用户信息表单弹窗 -->
        <SubmitInfoForm
            v-if="showSubmitForm"
            :data="submitFormData"
            @submit="handleFormSubmit"
            @close="closeSubmitForm"
        />
    </div>
</template>
<script setup>
const config = useRuntimeConfig();
const baseURL = config.public.NUXT_API_BASE;


const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: [],
                productList: []
            }
        }
    }
})

const activeTab = ref(0);
const showSubmitForm = ref(false);
const currentDownloadFile = ref(null);
const userHasSubmitted = ref(false);
const tabs = ref([]);

const { data } = await useAsyncData(
    () => $fetch(baseURL + '/product-documents/public/list')
)

const listData = data.value.data;

// 处理从API获取的数据，转换为tab和list格式
const processListData = () => {
    if (!listData || !Array.isArray(listData)) return;
    
    // 创建一个映射来存储系列名称和对应的文件
    const seriesMap = new Map();
    
    listData.forEach(item => {
        // 如果系列不存在，创建它
        if (!seriesMap.has(item.seriesName)) {
            seriesMap.set(item.seriesName, {
                name: item.seriesName,
                files: []
            });
        }
        
        const series = seriesMap.get(item.seriesName);
        
        // 添加文档文件
        if (item.documentFile) {
            series.files.push({
                name: item.name + ' 产品单页',
                url: item.documentFile.url,
                type: 'document'
            });
        }
        
        // 添加模型文件
        if (item.modelFile) {
            series.files.push({
                name: item.name + ' 产品模型',
                url: item.modelFile.url,
                type: 'model'
            });
        }
    });
    
    // 转换为数组格式
    tabs.value = Array.from(seriesMap.values());
};

// 初始化时处理数据
processListData();

const res = await useAsyncData(
    () => $fetch(baseURL + '/components/component-prop-data-by-name?name=ContactUs')
)

const contactData = res.data.value.data;

const submitFormData = ref({
    title: '即将下载',
    type: 'modal',
    privacyFile:contactData.privacyFile,
    usePolicy:contactData.usePolicy,
    productList:contactData.productList,
    downloadFile: null // 添加downloadFile字段
})

// 检查用户是否已经提交过信息
onMounted(() => {
    const hasSubmitted = localStorage.getItem('user_has_submitted_download_form');
    userHasSubmitted.value = hasSubmitted === 'true';
});

const curFiles = computed(() => {
    // 如果有从API获取的数据，使用它；否则使用props数据
    if (tabs.value.length > 0) {
        return tabs.value[activeTab.value]?.files || [];
    }
    return props.data.list[activeTab.value]?.files || [];
});

// // 表单数据配置
// const submitFormData = computed(() => ({
//     title: '下载资料',
//     type: 'modal',
//     productList: props.data.productList || [],
//     privacyFile: props.data.privacyFile || '',
//     usePolicy: props.data.usePolicy || '',
//     downloadFile: currentDownloadFile.value // 传递当前要下载的文件信息
// }));

// 直接下载文件的函数
const directDownload = (file) => {
    const a = document.createElement('a');
    a.href = $patchRawUrl(file.url);
    // 使用传入的文件名参数，如果没有则使用默认名称
    const fileName = file.name || 'download';
    a.download = fileName;
    document.body.appendChild(a);
    a.rel = 'noopener noreferrer';
    a.click();
    document.body.removeChild(a);
};

// 处理下载点击
const handleDownload = (file) => {
    currentDownloadFile.value = file;

    // 每次点击时都检查用户是否已经提交过信息
    const hasSubmitted = localStorage.getItem('user_has_submitted_download_form');
    userHasSubmitted.value = hasSubmitted === 'true';

    // 如果用户已经提交过信息，直接下载
    if (userHasSubmitted.value) {
        console.log('用户已提交过信息，直接下载文件:', file.name);
        directDownload(file);
        return;
    }

    // 否则显示表单，并传递要下载的文件信息
    console.log('用户首次下载，显示表单');
    localStorage.setItem('tmp_download_file', JSON.stringify(file));
    submitFormData.value.downloadFile = file; // 将文件信息传递给表单
    showSubmitForm.value = true;
};

// 处理表单提交
const handleFormSubmit = (formData) => {
    console.log('用户提交的信息:', formData);

    // 记录用户已经提交过信息
    userHasSubmitted.value = true;
    localStorage.setItem('user_has_submitted_download_form', 'true');

    // 表单提交成功后，关闭表单
    showSubmitForm.value = false;
};

// 关闭表单
const closeSubmitForm = () => {
    showSubmitForm.value = false;
    currentDownloadFile.value = null;
    // 清理临时存储的文件信息
    localStorage.removeItem('tmp_download_file');
};

</script>
<style lang="less" scoped>
.file-download {
    max-width: 1440px;
    // max-height: 1626px;
    margin: 0 auto;
    margin-bottom: var(--component-mb);

    .title {
        max-width: 816px;
        text-align: center;
        color: rgba(0, 0, 0, 0.80);
        font-family: "PingFang SC";
        font-size: 48px;
        font-style: normal;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        margin: 0 auto 40px;
    }

    .tab-bar {
        max-width: 1440px;
        margin: 0 auto 40px;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #D8D8D8;
        gap: 40px;

        .tab-item {
            display: inline-flex;
            height: 56px;
            padding: 0 24px;
            justify-content: center;
            align-items: center;
            color: rgba(0, 0, 0, 0.80);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 18px;
            font-weight: var(--sub-title-font-weight);
            line-height: 27px;
            cursor: pointer;
            position: relative;
            white-space: nowrap;

            &.active {
                font-weight: var(--title-font-weight);

                &::after {
                    content: '';
                    display: block;
                    width: 100%;
                    height: 1px;
                    background: #DF0428;
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                }
            }
        }
    }


    .file-items {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .file-item {
            display: flex;
            align-items: center;
            padding: 0 24px;
            max-width: 708px;
            width: 100%;
            height: 100px;
            border-radius: 12px;
            background: #FFF;
            flex: 1 1 30%;

            .file-type-icon {
                margin-right: 12px;
                flex: 0 0 auto;
            }

            .file-name {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 16px;
                font-weight: var(--title-font-weight);
                line-height: 32px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-right: 24px;
            }

            .download-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 128px;
                height: 40px;
                border-radius: 32px;
                border: 1px solid rgba(0, 0, 0, 0.80);

                cursor: pointer;
                margin-left: auto;
                flex: 0 0 auto;

                .download-btn-link {
                    color: rgba(0, 0, 0, 0.80);
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 21px;
                }

                &:hover {
                    background: rgba(0, 0, 0, 0.80);
                    color: rgba(255, 255, 255, 0.97);
                    border-radius: 32px;

                    .download-btn-link {
                        color: rgba(255, 255, 255, 0.97);
                    }

                    .icon-download {
                        background-color: rgba(255, 255, 255, 0.97);
                    }
                }
            }
        }
    }

    .icon-download {
        background-color: rgba(0, 0, 0, 0.80);
        margin-left: 4px;
    }

}

@media (max-width: 768px) {
    .file-download {
        margin: 0 16px;
        border-radius: 12px;
        margin-bottom: var(--component-mb);

        .title {
            font-size: 18px;
            line-height: 21px;
        }

        .tab-bar {
            flex-wrap: wrap;
            justify-content: flex-start;
            margin: 0 auto 12px;
            gap: 12px;

            .tab-item {
                height: 40px;
                padding: 0 10px;
                font-size: 14px;
                line-height: 21px;
            }
        }

        .file-items {
            gap: 16px;

            .file-item {
                display: flex;
                align-items: flex-start;
                padding: 14px 14px 12px;
                height: 98px;
                border-radius: 8px;
                position: relative;
                flex: 1 1 auto;

                .file-type-icon {
                    width: 32px;
                    height: 32px;
                }

                .file-name {
                    font-size: 14px;
                    line-height: 32px;
                    margin-right: 0;
                }

                .download-btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 128px;
                    height: 32px;
                    font-size: 12px;
                    line-height: 18px;
                    cursor: initial;
                    position: absolute;
                    right: 12px;
                    bottom: 12px;

                    &:hover {
                        background: rgba(0, 0, 0, 0.80);
                        color: rgba(255, 255, 255, 0.97);
                        border-radius: 32px;

                        .icon-download {
                            background-color: rgba(255, 255, 255, 0.97);
                        }
                    }
                }
            }
        }
    }
}
</style>