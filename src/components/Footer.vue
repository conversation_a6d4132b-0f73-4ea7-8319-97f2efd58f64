<template>
    <footer class="footer" @click="qrCodeVisible = false">
        <div class="up-top" @click="handleScrollTop">
            <span>回到顶部</span>
            <i class="icon-up-top" style="margin-left: 4px;"></i>
        </div>
        <!-- 产品导航 -->
        <div class="footer-top">
            <div class="footer-section" v-for="(section, index) in data.footerSections" :key="index">
                <div class="sub-menu-container" v-for="(sub, subIndex) in section.subcategories" :key="subIndex">
                    <h3 class="sub-title" v-if="sub.items" @click="handleToggle">{{ sub.title }}</h3>
                    <h3 class="sub-title no-items" v-else>
                        <a class="item-link" :href="sub.link">{{ sub.title }}</a>
                    </h3>
                    <ul class="sub-items" v-if="sub.items">
                        <li class="sub-item" v-for="(item, itemIndex) in sub.items" :key="itemIndex">
                            <a class="item-link" :href="item.link">{{ item.title }}</a>
                        </li>
                    </ul>
                </div>
            </div>
            <!-- <div class="footer-section">
                <div class="sub-menu-container">
                    <h3 class="sub-title no-items">
                        <i class="icon-global" style="margin-right: 8px;"></i>
                        <a class="item-link" target="_blank" href="https://global.agilex.ai/">中/EN</a>
                    </h3>
                </div>
            </div> -->
        </div>

        <template v-if="!isMobile">
            <!-- 二维码和社交图标 -->
            <div class="footer-icons">
                <div class="social-icons">
                    <a class="social-link bilibili" target="_blank" :href="data.bilibili">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M3.36842 3.78947C2.43826 3.78947 1.68421 4.54352 1.68421 5.47368V10.9474C1.68421 11.8775 2.43826 12.6316 3.36842 12.6316H12.6316C13.5617 12.6316 14.3158 11.8775 14.3158 10.9474V5.47368C14.3158 4.54352 13.5617 3.78947 12.6316 3.78947H3.36842ZM0 5.47368C0 3.61336 1.50809 2.10526 3.36842 2.10526H12.6316C14.4919 2.10526 16 3.61336 16 5.47368V10.9474C16 12.8077 14.4919 14.3158 12.6316 14.3158H3.36842C1.50809 14.3158 0 12.8077 0 10.9474V5.47368Z"
                                fill="#6C7073" />
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M3.19402 0.246647C3.52288 -0.0822157 4.05607 -0.0822157 4.38493 0.246647L6.4902 2.35191C6.81906 2.68077 6.81906 3.21396 6.4902 3.54283C6.16133 3.87169 5.62814 3.87169 5.29928 3.54283L3.19402 1.43756C2.86515 1.1087 2.86515 0.575509 3.19402 0.246647ZM12.806 0.246647C13.1348 0.575509 13.1348 1.1087 12.806 1.43756L10.7007 3.54283C10.3719 3.87169 9.83867 3.87169 9.5098 3.54283C9.18094 3.21396 9.18094 2.68077 9.5098 2.35191L11.6151 0.246647C11.9439 -0.0822157 12.4771 -0.0822157 12.806 0.246647ZM5.05263 6.73684C5.51771 6.73684 5.89474 7.11387 5.89474 7.57895V8.42105C5.89474 8.88614 5.51771 9.26316 5.05263 9.26316C4.58755 9.26316 4.21053 8.88614 4.21053 8.42105V7.57895C4.21053 7.11387 4.58755 6.73684 5.05263 6.73684ZM10.9474 6.73684C11.4125 6.73684 11.7895 7.11387 11.7895 7.57895V8.42105C11.7895 8.88614 11.4125 9.26316 10.9474 9.26316C10.4823 9.26316 10.1053 8.88614 10.1053 8.42105V7.57895C10.1053 7.11387 10.4823 6.73684 10.9474 6.73684Z"
                                fill="#6C7073" />
                        </svg>
                    </a>
                    <a class="social-link weibo" target="_blank" :href="data.weibo">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8.19922 18.2722C8.19922 21.026 11.3857 22.5995 14.6902 22.5995C18.8209 22.5995 21.7713 20.2887 21.7713 17.9284C21.7713 16.4967 20.5865 16.0672 20.0511 15.8731C19.9244 15.8272 19.8341 15.7944 19.8043 15.7647C19.7438 15.7041 19.7856 15.5371 19.841 15.316C19.9654 14.8192 20.1583 14.0494 19.4109 13.601C18.7713 13.2173 17.5493 13.5823 16.7189 13.8303C16.2724 13.9637 15.9392 14.0632 15.8704 13.9944C15.8165 13.9405 15.8512 13.7732 15.8976 13.5492C16.0207 12.9555 16.2261 11.9642 15.0836 11.6341C13.1941 11.0876 8.19922 15.125 8.19922 18.2722ZM14.8108 21.5302C17.371 21.2694 19.3065 19.6808 19.1334 17.9825C18.9604 16.2839 16.7444 15.1186 14.1838 15.3799C11.6236 15.6407 9.68806 17.2292 9.86115 18.9275C10.0342 20.6262 12.2502 21.7914 14.8108 21.5302Z"
                                fill="#6C7073" />
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M16.5555 18.1838C16.8336 19.3129 15.9945 20.4911 14.6814 20.8148C13.3682 21.1386 12.0779 20.4856 11.7994 19.3565C11.5212 18.2275 12.3599 17.0493 13.6735 16.7255C14.9866 16.4017 16.2774 17.0548 16.5555 18.1838ZM14.0606 18.7137C14.2714 19.1048 14.074 19.6205 13.62 19.866C13.1652 20.1115 12.6259 19.9934 12.4146 19.6024C12.2038 19.2118 12.4009 18.696 12.8552 18.4509C13.31 18.2055 13.8493 18.3231 14.0606 18.7137ZM15.0927 18.2065C15.1754 18.3596 15.098 18.5618 14.9199 18.658C14.7417 18.7542 14.5302 18.7081 14.4475 18.555C14.3648 18.4019 14.4422 18.1997 14.6204 18.1035C14.7985 18.0073 15.01 18.0534 15.0927 18.2065Z"
                                fill="#6C7073" />
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M18.4824 9.5311C18.6592 9.51012 18.8383 9.5 19.0187 9.5C21.5825 9.5 23.6608 11.5783 23.6608 14.1421C23.6608 14.3427 23.648 14.5413 23.6232 14.7357C23.5876 15.0159 23.3315 15.2141 23.0514 15.1785C22.7712 15.1428 22.5729 14.8868 22.6086 14.6066C22.6279 14.4548 22.6379 14.2994 22.6379 14.1421C22.6379 12.1432 21.0176 10.5228 19.0187 10.5228C18.8765 10.5228 18.7378 10.5308 18.603 10.5468C18.3225 10.5801 18.0682 10.3797 18.0349 10.0992C18.0016 9.81875 18.202 9.56439 18.4824 9.5311ZM19.5615 12.5808C19.3141 12.4949 19.0499 12.4691 18.7906 12.5053C18.5109 12.5444 18.2524 12.3494 18.2133 12.0697C18.1741 11.79 18.3692 11.5315 18.6489 11.4924C19.0687 11.4336 19.4965 11.4755 19.897 11.6146C20.2975 11.7537 20.6591 11.9859 20.9522 12.2922C21.2453 12.5985 21.4614 12.9701 21.5827 13.3763C21.704 13.7825 21.727 14.2117 21.6499 14.6285C21.5984 14.9063 21.3316 15.0897 21.0539 15.0383C20.7762 14.9869 20.5927 14.7201 20.6441 14.4424C20.6918 14.1849 20.6775 13.9198 20.6026 13.6689C20.5277 13.418 20.3942 13.1885 20.2132 12.9993C20.0322 12.8102 19.8088 12.6667 19.5615 12.5808Z"
                                fill="#6C7073" />
                        </svg>
                    </a>
                    <a class="social-link douyin" target="_blank" :href="data.tiktok">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M22.2234 11.9329C21.3431 11.9329 20.4989 11.5818 19.8764 10.9566C19.254 10.3315 18.9043 9.48366 18.9043 8.59961H16.1383V18.8774C16.1383 19.262 16.0247 19.6379 15.812 19.9577C15.5992 20.2774 15.2969 20.5266 14.9431 20.6738C14.5893 20.821 14.2 20.8595 13.8244 20.7845C13.4488 20.7094 13.1038 20.5243 12.833 20.2523C12.5623 19.9804 12.3779 19.6339 12.3032 19.2567C12.2285 18.8795 12.2668 18.4886 12.4133 18.1333C12.5599 17.778 12.808 17.4743 13.1265 17.2606C13.4449 17.047 13.8192 16.9329 14.2021 16.9329H15.0319V14.1552H14.2021C13.2721 14.1552 12.363 14.4321 11.5898 14.951C10.8165 15.4699 10.2138 16.2074 9.85793 17.0703C9.50204 17.9331 9.40892 18.8826 9.59035 19.7986C9.77178 20.7147 10.2196 21.5561 10.8772 22.2165C11.5348 22.8769 12.3727 23.3267 13.2848 23.5089C14.1969 23.6911 15.1424 23.5976 16.0016 23.2402C16.8608 22.8827 17.5951 22.2775 18.1118 21.5009C18.6285 20.7243 18.9043 19.8114 18.9043 18.8774V13.7224C19.8913 14.3691 21.0448 14.7126 22.2234 14.7107H22.5V11.9329H22.2234Z"
                                fill="#6C7073" />
                        </svg>
                    </a>
                    <a class="social-link xiaohongshu" target="_blank" :href="data.xhs">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M11.9221 9.38904H9.83488V12.3111H11.9221V14.8158H9V17.7379H11.9221V23.9995H14.8442V17.7379H19.8665C20.2526 17.7379 20.2709 17.7685 20.2709 18.1553V20.66C20.2709 21.0468 20.2526 21.0774 19.8665 21.0774H16.9314C16.9314 22.6912 18.2527 23.9995 19.8665 23.9995C21.9407 23.9995 23.193 23.1646 23.193 21.0774V18.1553C23.193 16.0681 21.5233 14.8158 19.436 14.8158V12.7286C19.436 10.5365 17.8668 9.38904 15.6791 9.38904H15.2616C15.0311 9.38904 14.8442 9.20215 14.8442 8.9716V8.13672H11.9221V9.38904ZM16.0965 14.8158C16.3271 14.8158 16.514 14.6289 16.514 14.3983V12.7286C16.514 12.3417 16.3767 12.3111 16.0965 12.3111H14.8442V14.3983C14.8442 14.6289 15.0311 14.8158 15.2616 14.8158H16.0965Z"
                                fill="#6C7073" />
                            <path
                                d="M23.1916 10.4337C23.1916 11.2406 22.5375 11.8947 21.7306 11.8947C21.3218 11.8947 20.2695 11.8947 20.2695 11.8947C20.2695 11.8947 20.2695 10.8318 20.2695 10.4337C20.2695 9.62679 20.9237 8.97266 21.7306 8.97266C22.5375 8.97266 23.1916 9.62679 23.1916 10.4337Z"
                                fill="#6C7073" />
                        </svg>
                    </a>
                    <a class="social-link weixin">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M19.2197 13.8969L19.1029 13.8957C16.4662 13.8957 14.2332 15.7649 14.2332 18.1811C14.2332 18.5734 14.292 18.9509 14.4019 19.3085C14.1518 19.336 13.9004 19.3498 13.6488 19.3498C12.9441 19.3498 12.2697 19.243 11.6476 19.0479C11.4941 18.9999 10.9354 19.319 10.4637 19.589C10.1025 19.7955 9.79204 19.9731 9.75308 19.9341C9.71646 19.8975 9.81892 19.6544 9.93852 19.37C10.1123 18.9571 10.3226 18.4561 10.1972 18.3758C8.86096 17.5207 8 16.181 8 14.6749C8 12.0932 10.5291 10 13.6488 10C16.4483 10 18.7721 11.6853 19.2197 13.8969ZM12.6749 13.1166C12.6749 13.3232 12.5928 13.5214 12.4467 13.6675C12.3006 13.8137 12.1024 13.8957 11.8957 13.8957C11.6891 13.8957 11.4909 13.8137 11.3448 13.6675C11.1987 13.5214 11.1166 13.3232 11.1166 13.1166C11.1166 12.91 11.1987 12.7118 11.3448 12.5657C11.4909 12.4195 11.6891 12.3374 11.8957 12.3374C12.1024 12.3374 12.3006 12.4195 12.4467 12.5657C12.5928 12.7118 12.6749 12.91 12.6749 13.1166ZM15.4019 13.8957C15.6086 13.8957 15.8067 13.8137 15.9529 13.6675C16.099 13.5214 16.1811 13.3232 16.1811 13.1166C16.1811 12.91 16.099 12.7118 15.9529 12.5657C15.8067 12.4195 15.6086 12.3374 15.4019 12.3374C15.1953 12.3374 14.9971 12.4195 14.851 12.5657C14.7049 12.7118 14.6228 12.91 14.6228 13.1166C14.6228 13.3232 14.7049 13.5214 14.851 13.6675C14.9971 13.8137 15.1953 13.8957 15.4019 13.8957Z"
                                fill="#6C7073" />
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M23.5852 18.1809C23.5852 19.4906 22.8419 20.6492 21.7024 21.3555C21.6518 21.3867 21.7682 21.6898 21.8761 21.9722C21.9685 22.213 22.055 22.4382 22.0269 22.4662C21.9977 22.4954 21.7145 22.3388 21.4149 22.1733C21.0919 21.9948 20.7503 21.8063 20.686 21.8273C20.1944 21.9882 19.6614 22.0766 19.1051 22.0766C16.6309 22.0766 14.625 20.3325 14.625 18.1809C14.625 16.0293 16.6309 14.2852 19.1051 14.2852C21.5793 14.2852 23.5852 16.0293 23.5852 18.1809ZM21.2478 16.8174C21.2478 16.9724 21.1862 17.121 21.0766 17.2306C20.967 17.3402 20.8184 17.4018 20.6634 17.4018C20.5084 17.4018 20.3598 17.3402 20.2502 17.2306C20.1406 17.121 20.079 16.9724 20.079 16.8174C20.079 16.6624 20.1406 16.5138 20.2502 16.4042C20.3598 16.2946 20.5084 16.233 20.6634 16.233C20.8184 16.233 20.967 16.2946 21.0766 16.4042C21.1862 16.5138 21.2478 16.6624 21.2478 16.8174ZM17.5468 17.4018C17.7018 17.4018 17.8504 17.3402 17.96 17.2306C18.0696 17.121 18.1312 16.9724 18.1312 16.8174C18.1312 16.6624 18.0696 16.5138 17.96 16.4042C17.8504 16.2946 17.7018 16.233 17.5468 16.233C17.3918 16.233 17.2432 16.2946 17.1336 16.4042C17.024 16.5138 16.9624 16.6624 16.9624 16.8174C16.9624 16.9724 17.024 17.121 17.1336 17.2306C17.2432 17.3402 17.3918 17.4018 17.5468 17.4018Z"
                                fill="#6C7073" />
                        </svg>
                        <div class="qrcode">
                            <div class="qrcode-tips">微信公众号</div>
                            <img :src="patchRawUrl(data.wechat.url)" alt="二维码" />
                        </div>
                    </a>
                </div>
            </div>

            <!-- 联系方式 -->
            <div class="footer-contact">
                <div class="contact-card">
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                            <g clip-path="url(#clip0_987_1977)">
                                <path
                                    d="M8.7777 2.91732C9.34747 3.02848 9.8711 3.30713 10.2816 3.71761C10.6921 4.12809 10.9707 4.65172 11.0819 5.22148M8.7777 0.583984C9.96145 0.715489 11.0653 1.24559 11.908 2.08724C12.7507 2.92889 13.2822 4.03207 13.4152 5.21565M12.8319 9.87065V11.6207C12.8325 11.7831 12.7993 11.9439 12.7342 12.0928C12.6691 12.2416 12.5736 12.3752 12.4539 12.4851C12.3342 12.5949 12.1929 12.6785 12.039 12.7306C11.8851 12.7826 11.722 12.8019 11.5602 12.7873C9.76519 12.5923 8.04095 11.9789 6.52604 10.9965C5.1166 10.1009 3.92165 8.90592 3.02604 7.49648C2.04019 5.97469 1.42668 4.24206 1.2352 2.43898C1.22063 2.27767 1.2398 2.11509 1.2915 1.9616C1.34319 1.8081 1.42629 1.66705 1.53548 1.54743C1.64468 1.42781 1.77759 1.33223 1.92575 1.26679C2.07391 1.20135 2.23407 1.16747 2.39604 1.16732H4.14604C4.42913 1.16453 4.70358 1.26478 4.91823 1.44938C5.13288 1.63398 5.27308 1.89033 5.3127 2.17065C5.38657 2.73069 5.52355 3.28058 5.72104 3.80982C5.79952 4.01861 5.81651 4.24552 5.76998 4.46366C5.72346 4.68181 5.61537 4.88205 5.45854 5.04065L4.7177 5.78148C5.54811 7.24189 6.7573 8.45108 8.2177 9.28148L8.95854 8.54065C9.11714 8.38381 9.31738 8.27573 9.53552 8.22921C9.75367 8.18268 9.98058 8.19967 10.1894 8.27815C10.7186 8.47564 11.2685 8.61262 11.8285 8.68648C12.1119 8.72646 12.3707 8.86919 12.5557 9.08752C12.7407 9.30586 12.839 9.58457 12.8319 9.87065Z"
                                    stroke="#252525" stroke-linecap="round" stroke-linejoin="round" />
                            </g>
                            <defs>
                                <clipPath id="clip0_987_1977">
                                    <rect width="14" height="14" fill="white" />
                                </clipPath>
                            </defs>
                        </svg>
                    </span>
                    <p>{{ data.phone }}</p>
                    <div class="btn" @click="copyText(data.phone)">客服热线（复制）</div>
                    <div v-show="copySuccess" class="floating-tip">复制成功！</div>
                </div>
                <div class="contact-card">
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                            <path
                                d="M2.33464 2.33398H11.668C12.3096 2.33398 12.8346 2.85898 12.8346 3.50065V10.5007C12.8346 11.1423 12.3096 11.6673 11.668 11.6673H2.33464C1.69297 11.6673 1.16797 11.1423 1.16797 10.5007V3.50065C1.16797 2.85898 1.69297 2.33398 2.33464 2.33398Z"
                                stroke="#252525" stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M12.8346 3.5L7.0013 7.58333L1.16797 3.5" stroke="#252525" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </span>
                    <p>{{ data.mail }}</p>
                    <div class="btn" @click="copyText(data.mail)">邮箱（复制）</div>
                    <div v-show="copySuccess" class="floating-tip">复制成功！</div>
                </div>
                <div class="contact-card">
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                            <g clip-path="url(#clip0_987_1992)">
                                <path d="M12.8346 1.16602L6.41797 7.58268" stroke="#252525" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path
                                    d="M12.8346 1.16602L8.7513 12.8327L6.41797 7.58268L1.16797 5.24935L12.8346 1.16602Z"
                                    stroke="#252525" stroke-linecap="round" stroke-linejoin="round" />
                            </g>
                            <defs>
                                <clipPath id="clip0_987_1992">
                                    <rect width="14" height="14" fill="white" />
                                </clipPath>
                            </defs>
                        </svg>
                    </span>
                    <p>第一时间获得松灵最新动态</p>
                    <div class="input">
                        <input type="text" placeholder="请输入您的邮箱地址" v-model="email" />
                        <div class="email-icon" @click="submitEmail()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                                fill="none">
                                <path
                                    d="M3.33464 3.33398H16.668C17.5846 3.33398 18.3346 4.08398 18.3346 5.00065V15.0007C18.3346 15.9173 17.5846 16.6673 16.668 16.6673H3.33464C2.41797 16.6673 1.66797 15.9173 1.66797 15.0007V5.00065C1.66797 4.08398 2.41797 3.33398 3.33464 3.33398Z"
                                    stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M18.3346 5L10.0013 10.8333L1.66797 5" stroke="white" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </div>
                    </div>
                    <div v-show="emailSubmitMessage" class="floating-tip">{{ emailSubmitMessage }}</div>
                </div>
            </div>

            <!-- 版权信息 -->
            <div class="footer-copyright">
                <p>{{ data.copyright }}</p>
            </div>

            <div class="footer-bottom-text" @mousemove="handleMouseMove">
                <div class="noise-mask"></div>
                <div ref="spotlight" class="spotlight"></div>
            </div>
        </template>
        <template v-else>
            <div class="footer-contact">
                <div class="contact-card">
                    <div class="type-title">订阅我们</div>
                    <div class="desc">第一时间获得松灵最新动态</div>
                    <div class="input">
                        <input type="text" placeholder="请输入您的邮箱地址" />
                        <div class="email-icon" @click="submitEmail()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                                fill="none">
                                <path
                                    d="M3.33464 3.33398H16.668C17.5846 3.33398 18.3346 4.08398 18.3346 5.00065V15.0007C18.3346 15.9173 17.5846 16.6673 16.668 16.6673H3.33464C2.41797 16.6673 1.66797 15.9173 1.66797 15.0007V5.00065C1.66797 4.08398 2.41797 3.33398 3.33464 3.33398Z"
                                    stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M18.3346 5L10.0013 10.8333L1.66797 5" stroke="white" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="footer-icons">
                    <div class="social-icons">
                        <a class="social-link bilibili" target="_blank" :href="data.bilibili">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15"
                                fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M3.36842 3.78947C2.43826 3.78947 1.68421 4.54352 1.68421 5.47368V10.9474C1.68421 11.8775 2.43826 12.6316 3.36842 12.6316H12.6316C13.5617 12.6316 14.3158 11.8775 14.3158 10.9474V5.47368C14.3158 4.54352 13.5617 3.78947 12.6316 3.78947H3.36842ZM0 5.47368C0 3.61336 1.50809 2.10526 3.36842 2.10526H12.6316C14.4919 2.10526 16 3.61336 16 5.47368V10.9474C16 12.8077 14.4919 14.3158 12.6316 14.3158H3.36842C1.50809 14.3158 0 12.8077 0 10.9474V5.47368Z"
                                    fill="#6C7073" />
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M3.19402 0.246647C3.52288 -0.0822157 4.05607 -0.0822157 4.38493 0.246647L6.4902 2.35191C6.81906 2.68077 6.81906 3.21396 6.4902 3.54283C6.16133 3.87169 5.62814 3.87169 5.29928 3.54283L3.19402 1.43756C2.86515 1.1087 2.86515 0.575509 3.19402 0.246647ZM12.806 0.246647C13.1348 0.575509 13.1348 1.1087 12.806 1.43756L10.7007 3.54283C10.3719 3.87169 9.83867 3.87169 9.5098 3.54283C9.18094 3.21396 9.18094 2.68077 9.5098 2.35191L11.6151 0.246647C11.9439 -0.0822157 12.4771 -0.0822157 12.806 0.246647ZM5.05263 6.73684C5.51771 6.73684 5.89474 7.11387 5.89474 7.57895V8.42105C5.89474 8.88614 5.51771 9.26316 5.05263 9.26316C4.58755 9.26316 4.21053 8.88614 4.21053 8.42105V7.57895C4.21053 7.11387 4.58755 6.73684 5.05263 6.73684ZM10.9474 6.73684C11.4125 6.73684 11.7895 7.11387 11.7895 7.57895V8.42105C11.7895 8.88614 11.4125 9.26316 10.9474 9.26316C10.4823 9.26316 10.1053 8.88614 10.1053 8.42105V7.57895C10.1053 7.11387 10.4823 6.73684 10.9474 6.73684Z"
                                    fill="#6C7073" />
                            </svg>
                        </a>
                        <a class="social-link weibo" target="_blank" :href="data.weibo">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"
                                fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M8.19922 18.2722C8.19922 21.026 11.3857 22.5995 14.6902 22.5995C18.8209 22.5995 21.7713 20.2887 21.7713 17.9284C21.7713 16.4967 20.5865 16.0672 20.0511 15.8731C19.9244 15.8272 19.8341 15.7944 19.8043 15.7647C19.7438 15.7041 19.7856 15.5371 19.841 15.316C19.9654 14.8192 20.1583 14.0494 19.4109 13.601C18.7713 13.2173 17.5493 13.5823 16.7189 13.8303C16.2724 13.9637 15.9392 14.0632 15.8704 13.9944C15.8165 13.9405 15.8512 13.7732 15.8976 13.5492C16.0207 12.9555 16.2261 11.9642 15.0836 11.6341C13.1941 11.0876 8.19922 15.125 8.19922 18.2722ZM14.8108 21.5302C17.371 21.2694 19.3065 19.6808 19.1334 17.9825C18.9604 16.2839 16.7444 15.1186 14.1838 15.3799C11.6236 15.6407 9.68806 17.2292 9.86115 18.9275C10.0342 20.6262 12.2502 21.7914 14.8108 21.5302Z"
                                    fill="#6C7073" />
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M16.5555 18.1838C16.8336 19.3129 15.9945 20.4911 14.6814 20.8148C13.3682 21.1386 12.0779 20.4856 11.7994 19.3565C11.5212 18.2275 12.3599 17.0493 13.6735 16.7255C14.9866 16.4017 16.2774 17.0548 16.5555 18.1838ZM14.0606 18.7137C14.2714 19.1048 14.074 19.6205 13.62 19.866C13.1652 20.1115 12.6259 19.9934 12.4146 19.6024C12.2038 19.2118 12.4009 18.696 12.8552 18.4509C13.31 18.2055 13.8493 18.3231 14.0606 18.7137ZM15.0927 18.2065C15.1754 18.3596 15.098 18.5618 14.9199 18.658C14.7417 18.7542 14.5302 18.7081 14.4475 18.555C14.3648 18.4019 14.4422 18.1997 14.6204 18.1035C14.7985 18.0073 15.01 18.0534 15.0927 18.2065Z"
                                    fill="#6C7073" />
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M18.4824 9.5311C18.6592 9.51012 18.8383 9.5 19.0187 9.5C21.5825 9.5 23.6608 11.5783 23.6608 14.1421C23.6608 14.3427 23.648 14.5413 23.6232 14.7357C23.5876 15.0159 23.3315 15.2141 23.0514 15.1785C22.7712 15.1428 22.5729 14.8868 22.6086 14.6066C22.6279 14.4548 22.6379 14.2994 22.6379 14.1421C22.6379 12.1432 21.0176 10.5228 19.0187 10.5228C18.8765 10.5228 18.7378 10.5308 18.603 10.5468C18.3225 10.5801 18.0682 10.3797 18.0349 10.0992C18.0016 9.81875 18.202 9.56439 18.4824 9.5311ZM19.5615 12.5808C19.3141 12.4949 19.0499 12.4691 18.7906 12.5053C18.5109 12.5444 18.2524 12.3494 18.2133 12.0697C18.1741 11.79 18.3692 11.5315 18.6489 11.4924C19.0687 11.4336 19.4965 11.4755 19.897 11.6146C20.2975 11.7537 20.6591 11.9859 20.9522 12.2922C21.2453 12.5985 21.4614 12.9701 21.5827 13.3763C21.704 13.7825 21.727 14.2117 21.6499 14.6285C21.5984 14.9063 21.3316 15.0897 21.0539 15.0383C20.7762 14.9869 20.5927 14.7201 20.6441 14.4424C20.6918 14.1849 20.6775 13.9198 20.6026 13.6689C20.5277 13.418 20.3942 13.1885 20.2132 12.9993C20.0322 12.8102 19.8088 12.6667 19.5615 12.5808Z"
                                    fill="#6C7073" />
                            </svg>
                        </a>
                        <a class="social-link douyin" target="_blank" :href="data.tiktok">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"
                                fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M22.2234 11.9329C21.3431 11.9329 20.4989 11.5818 19.8764 10.9566C19.254 10.3315 18.9043 9.48366 18.9043 8.59961H16.1383V18.8774C16.1383 19.262 16.0247 19.6379 15.812 19.9577C15.5992 20.2774 15.2969 20.5266 14.9431 20.6738C14.5893 20.821 14.2 20.8595 13.8244 20.7845C13.4488 20.7094 13.1038 20.5243 12.833 20.2523C12.5623 19.9804 12.3779 19.6339 12.3032 19.2567C12.2285 18.8795 12.2668 18.4886 12.4133 18.1333C12.5599 17.778 12.808 17.4743 13.1265 17.2606C13.4449 17.047 13.8192 16.9329 14.2021 16.9329H15.0319V14.1552H14.2021C13.2721 14.1552 12.363 14.4321 11.5898 14.951C10.8165 15.4699 10.2138 16.2074 9.85793 17.0703C9.50204 17.9331 9.40892 18.8826 9.59035 19.7986C9.77178 20.7147 10.2196 21.5561 10.8772 22.2165C11.5348 22.8769 12.3727 23.3267 13.2848 23.5089C14.1969 23.6911 15.1424 23.5976 16.0016 23.2402C16.8608 22.8827 17.5951 22.2775 18.1118 21.5009C18.6285 20.7243 18.9043 19.8114 18.9043 18.8774V13.7224C19.8913 14.3691 21.0448 14.7126 22.2234 14.7107H22.5V11.9329H22.2234Z"
                                    fill="#6C7073" />
                            </svg>
                        </a>
                        <a class="social-link xiaohongshu" target="_blank" :href="data.xhs">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"
                                fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M11.9221 9.38904H9.83488V12.3111H11.9221V14.8158H9V17.7379H11.9221V23.9995H14.8442V17.7379H19.8665C20.2526 17.7379 20.2709 17.7685 20.2709 18.1553V20.66C20.2709 21.0468 20.2526 21.0774 19.8665 21.0774H16.9314C16.9314 22.6912 18.2527 23.9995 19.8665 23.9995C21.9407 23.9995 23.193 23.1646 23.193 21.0774V18.1553C23.193 16.0681 21.5233 14.8158 19.436 14.8158V12.7286C19.436 10.5365 17.8668 9.38904 15.6791 9.38904H15.2616C15.0311 9.38904 14.8442 9.20215 14.8442 8.9716V8.13672H11.9221V9.38904ZM16.0965 14.8158C16.3271 14.8158 16.514 14.6289 16.514 14.3983V12.7286C16.514 12.3417 16.3767 12.3111 16.0965 12.3111H14.8442V14.3983C14.8442 14.6289 15.0311 14.8158 15.2616 14.8158H16.0965Z"
                                    fill="#6C7073" />
                                <path
                                    d="M23.1916 10.4337C23.1916 11.2406 22.5375 11.8947 21.7306 11.8947C21.3218 11.8947 20.2695 11.8947 20.2695 11.8947C20.2695 11.8947 20.2695 10.8318 20.2695 10.4337C20.2695 9.62679 20.9237 8.97266 21.7306 8.97266C22.5375 8.97266 23.1916 9.62679 23.1916 10.4337Z"
                                    fill="#6C7073" />
                            </svg>
                        </a>
                        <a class="social-link weixin" @click.stop="qrCodeVisible = true">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"
                                fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M19.2197 13.8969L19.1029 13.8957C16.4662 13.8957 14.2332 15.7649 14.2332 18.1811C14.2332 18.5734 14.292 18.9509 14.4019 19.3085C14.1518 19.336 13.9004 19.3498 13.6488 19.3498C12.9441 19.3498 12.2697 19.243 11.6476 19.0479C11.4941 18.9999 10.9354 19.319 10.4637 19.589C10.1025 19.7955 9.79204 19.9731 9.75308 19.9341C9.71646 19.8975 9.81892 19.6544 9.93852 19.37C10.1123 18.9571 10.3226 18.4561 10.1972 18.3758C8.86096 17.5207 8 16.181 8 14.6749C8 12.0932 10.5291 10 13.6488 10C16.4483 10 18.7721 11.6853 19.2197 13.8969ZM12.6749 13.1166C12.6749 13.3232 12.5928 13.5214 12.4467 13.6675C12.3006 13.8137 12.1024 13.8957 11.8957 13.8957C11.6891 13.8957 11.4909 13.8137 11.3448 13.6675C11.1987 13.5214 11.1166 13.3232 11.1166 13.1166C11.1166 12.91 11.1987 12.7118 11.3448 12.5657C11.4909 12.4195 11.6891 12.3374 11.8957 12.3374C12.1024 12.3374 12.3006 12.4195 12.4467 12.5657C12.5928 12.7118 12.6749 12.91 12.6749 13.1166ZM15.4019 13.8957C15.6086 13.8957 15.8067 13.8137 15.9529 13.6675C16.099 13.5214 16.1811 13.3232 16.1811 13.1166C16.1811 12.91 16.099 12.7118 15.9529 12.5657C15.8067 12.4195 15.6086 12.3374 15.4019 12.3374C15.1953 12.3374 14.9971 12.4195 14.851 12.5657C14.7049 12.7118 14.6228 12.91 14.6228 13.1166C14.6228 13.3232 14.7049 13.5214 14.851 13.6675C14.9971 13.8137 15.1953 13.8957 15.4019 13.8957Z"
                                    fill="#6C7073" />
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M23.5852 18.1809C23.5852 19.4906 22.8419 20.6492 21.7024 21.3555C21.6518 21.3867 21.7682 21.6898 21.8761 21.9722C21.9685 22.213 22.055 22.4382 22.0269 22.4662C21.9977 22.4954 21.7145 22.3388 21.4149 22.1733C21.0919 21.9948 20.7503 21.8063 20.686 21.8273C20.1944 21.9882 19.6614 22.0766 19.1051 22.0766C16.6309 22.0766 14.625 20.3325 14.625 18.1809C14.625 16.0293 16.6309 14.2852 19.1051 14.2852C21.5793 14.2852 23.5852 16.0293 23.5852 18.1809ZM21.2478 16.8174C21.2478 16.9724 21.1862 17.121 21.0766 17.2306C20.967 17.3402 20.8184 17.4018 20.6634 17.4018C20.5084 17.4018 20.3598 17.3402 20.2502 17.2306C20.1406 17.121 20.079 16.9724 20.079 16.8174C20.079 16.6624 20.1406 16.5138 20.2502 16.4042C20.3598 16.2946 20.5084 16.233 20.6634 16.233C20.8184 16.233 20.967 16.2946 21.0766 16.4042C21.1862 16.5138 21.2478 16.6624 21.2478 16.8174ZM17.5468 17.4018C17.7018 17.4018 17.8504 17.3402 17.96 17.2306C18.0696 17.121 18.1312 16.9724 18.1312 16.8174C18.1312 16.6624 18.0696 16.5138 17.96 16.4042C17.8504 16.2946 17.7018 16.233 17.5468 16.233C17.3918 16.233 17.2432 16.2946 17.1336 16.4042C17.024 16.5138 16.9624 16.6624 16.9624 16.8174C16.9624 16.9724 17.024 17.121 17.1336 17.2306C17.2432 17.3402 17.3918 17.4018 17.5468 17.4018Z"
                                    fill="#6C7073" />
                            </svg>
                            <div class="qrcode" v-if="qrCodeVisible">
                                <img :src="$patchRawUrl(data.wechat.url)" alt="二维码" />
                            </div>
                        </a>
                    </div>
                </div>
                <div class="contact-card">
                    <div class="type-title">联系我们</div>
                    <div class="desc" style="margin-bottom: 4px;">
                        <i class="desc-icon icon-phone" style="margin-right: 8px;"></i>
                        <span class="desc-text">{{ data.phone }}</span>
                    </div>
                    <div class="desc" style="margin-bottom: 0px;">
                        <i class="desc-icon icon-email" style="margin-right: 8px;"></i>
                        <span class="desc-text">{{ data.mail }}</span>
                    </div>
                </div>
            </div>
            <div class="footer-copyright">
                <p>{{ data.copyright }}</p>
            </div>
        </template>

        <ax-modal ref="modal" :message="message" />

    </footer>
</template>

<script setup lang="ts">
const patchRawUrl = usePatchRawUrl();
const config = useRuntimeConfig();
const baseURL = config.public.NUXT_API_BASE;
const dataId = '68785fedcbfd11368ee83429';

const isMobile = ref(false);
onMounted(() => {
    isMobile.value = window.innerWidth < 768;
})

// const props = defineProps({
//     data: {
//         type: Object,
//         default: () => {
//             return {
//                 wechat: {},
//                 footerSections: {}
//             }
//         }
//     }
// })

const res: any = await useAsyncData(
    () => $fetch(baseURL + '/components/component-prop-data-by-name?name=Footer')
)

const data = res.data.value.data;

const copySuccess = ref(false);
const qrCodeVisible = ref(false);

const copyText = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        copySuccess.value = true;
        setTimeout(() => {
            copySuccess.value = false;
        }, 1000);
    } catch (err) {
        console.error('复制失败:', err);
    }
};


const message = ref('');
const modal = ref();
const email = ref('');
const emailSubmitMessage = ref('');
const submitEmail = async () => {
    if (!email.value) {
        emailSubmitMessage.value = '请输入邮箱';
        setTimeout(() => {
            emailSubmitMessage.value = '';
        }, 3000);
        return;
    }
    if (!email.value.match(/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/)) {
        emailSubmitMessage.value = '请输入正确的邮箱';
        setTimeout(() => {
            emailSubmitMessage.value = '';
        }, 3000);
        return;
    }
    try {
        await $fetch(baseURL + '/customer/subscribe', {
            method: 'POST',
            body: {
                email: email.value
            },
        }).then(() => {
            message.value = '订阅成功';
            modal.value.show();
        })
    } catch (err) {
        console.error('复制失败:', err);
        message.value = '订阅失败: 请检查邮箱格式是否正确';
        modal.value.show();
    }
}

const spotlight = ref<HTMLDivElement>();
const handleMouseMove = (e: MouseEvent) => {
    const container = e.currentTarget as HTMLElement;
    const rect = container.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 更新光照位置
    spotlight.value.setAttribute('style', `--mouse-x:${x};--mouse-y:${y};`)
    // spotlight.value.style.top = `${y}px`;
};

const handleToggle = (e: MouseEvent) => {
    if (window.innerWidth >= 768) return;

    const el = e.target as HTMLDListElement;
    el.closest('.sub-menu-container').classList.toggle('active');
};

const handleScrollTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}
</script>

<style lang="less" scoped>
.floating-tip {
    position: fixed;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    transition: opacity 0.3s;
    animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-50%);
    }

    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.floating-tip[aria-hidden="true"] {
    opacity: 0;
}

.footer {
    width: 100%;
    background-color: #000;
    color: #fff;
    font-family: sans-serif;
    padding: 80px 0 0;
    position: relative;

    .up-top {
        display: none;
    }

    .footer-top {
        display: flex;
        justify-content: space-between;
        // gap: 20px;
        // margin-bottom: 56px;
        margin: 0 10vw 56px;

        .footer-section {
            flex: 1;
            min-width: 120px;
        }

        .footer-section h3 {
            color: #FFF;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            margin-bottom: 16px;
        }

        .footer-section ul {
            list-style: none;
            padding: 0;
            margin: 0 0 24px 0;
        }

        .footer-section li {
            display: flex;
            align-items: center;
            height: 18px;
            color: rgba(255, 255, 255, 0.57);
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            margin-bottom: 8px;
            white-space: nowrap;
            cursor: pointer;

            &::after {
                content: "";
                background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none"><path d="M0.5 9.53516H18.5" stroke="white" stroke-opacity="0.97" stroke-linecap="round" /> <path d="M15.4645 13.0707L18.8586 9.67658C18.9367 9.59847 18.9367 9.47184 18.8586 9.39374L15.4645 5.99962" stroke="white" stroke-opacity="0.97" stroke-linecap="round" /> </svg>') no-repeat right center;
                display: block;
                width: 20px;
                height: 20px;
                margin-left: 8px;
                transform: translateX(-20px);
                opacity: 0;
                transition: all 0.2s ease-in-out;
                transition-delay: 0.1s;
            }

            &:hover {
                color: #fff;
            }

            &:hover::after {
                transform: translateX(0);
                opacity: 1;
            }


        }

        .item-link {
            color: inherit;
        }
    }


    .footer-icons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 279px;
        flex-direction: row-reverse;
        position: relative;

        .social-icons {
            display: flex;
            align-items: center;

            .social-link {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                margin-right: 12px;
                font-size: 20px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:last-child {
                    margin-right: 0;
                }

                &:hover svg {
                    filter: brightness(3);
                }

                .qrcode {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    width: 108px;
                    height: 142px;
                    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="108" height="142" viewBox="0 0 108 142" fill="none"><path d="M108 122.6C108 125.582 105.582 128 102.6 128L5.4 128C2.41766 128 1.39157e-06 125.582 1.65229e-06 122.6L1.18982e-05 5.4C1.2159e-05 2.41767 2.41768 -9.2303e-06 5.40001 -8.96958e-06L102.6 -4.72083e-07C105.582 -2.11359e-07 108 2.41768 108 5.40001L108 122.6Z" fill="white"/><path d="M56.336 140.15C55.2968 141.95 52.6987 141.95 51.6595 140.15L39.9681 119.9L68.0273 119.9L56.336 140.15Z" fill="white"/></svg>') no-repeat;
                    position: absolute;
                    bottom: 32px;
                    opacity: 0;
                    visibility: hidden;
                    cursor: initial;
                    transition: all 0.2s ease-in-out;

                    .qrcode-tips {
                        color: rgba(0, 0, 0, 0.80);
                        font-family: "PingFang SC";
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 18px;
                        margin: 8px auto 0;
                    }

                    img {
                        width: 97px;
                        height: 97px;
                    }
                }

                &:hover .qrcode {
                    opacity: 1;
                    visibility: visible;
                }
            }

        }


    }


    .footer-contact {
        display: flex;
        justify-content: space-around;
        align-items: stretch;
        gap: 20px;
        padding: 80px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.12);
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
        margin: 0 10vw;

        .contact-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #111;
            padding: 33px;
            border-radius: 12px;
            text-align: center;
            width: 454px;
            min-height: 211px;

            span {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 12px;
                background-color: #fff;
                margin-bottom: 24px;
            }

            p {
                color: rgba(255, 255, 255, 0.91);
                font-family: "PingFang SC";
                font-size: 22px;
                font-style: normal;
                font-weight: 500;
                line-height: 33px;
                margin-bottom: 24px;
            }

            .btn {
                display: inline-flex;
                height: 32px;
                padding: 2px 22px;
                justify-content: center;
                align-items: center;
                color: rgba(255, 255, 255, 0.57);
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 27px;
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.57);

                &:hover {
                    border: 1px solid rgba(255, 255, 255, 0.91);
                    color: rgba(255, 255, 255, 0.91);
                    background: rgba(255, 255, 255, 0.10);
                    cursor: pointer;
                }
            }

            .input {
                position: relative;

                input {
                    color: rgba(255, 255, 255, 0.57);
                    font-family: "PingFang SC";
                    padding: 8px;
                    width: 216px;
                    height: 32px;
                    background: #222;
                    color: #fff;
                    border-radius: 20px;
                    border: 1px solid #6C7073;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    padding-left: 16px;
                    padding-right: 32px;
                }

                .email-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 40px;
                    height: 100%;
                    border-radius: 0px 20px 20px 0px;
                    background: #6C7073;
                    cursor: pointer;
                }
            }
        }
    }

    .footer-copyright {
        text-align: center;
        color: #6C7073;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-top: 24px;
        margin-bottom: 80px;
    }


    .footer-bottom-text {
        width: 100%;
        background: #000;
        aspect-ratio: 1920/422;
        mask-image: url(../assets/imgs/footer_bottom_text.png);
        mask-size: contain;
        mask-repeat: no-repeat;
        image-rendering: crisp-edges;
        position: relative;
        transition: all 1s cubic-bezier(.165, .84, .44, 1);
        transform-origin: bottom;
        overflow: hidden;

        .noise-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url(../assets/imgs/noise.png) repeat;
            z-index: 5;
        }

        .spotlight {
            position: absolute;
            filter: blur(100px);
            width: 70rem;
            height: 70rem;
            aspect-ratio: 1/1;
            border-radius: 100%;
            transition: opacity 1s cubic-bezier(.165, .84, .44, 1);
            background: radial-gradient(circle at center, rgba(255, 255, 255, .2), rgba(255, 255, 255, .1), rgba(255, 255, 255, 0));
            transform: translate3d(calc(var(--mouse-x, -100%) * 1px - 35rem), calc(var(--mouse-y, -100%) * 1px - 35rem), 0);
        }
    }
}

@media (max-width: 768px) {
    .footer {
        overflow: hidden;
        padding: 88px 0 122px;

        .up-top {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.57);
            font-family: "PingFang SC";
            font-size: 10px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            padding: 10px;
            position: absolute;
            top: 24px;
            left: 50%;
            transform: translateX(-50%);
        }

        .footer-top {
            display: flex;
            flex-direction: column;
            justify-content: initial;
            margin: 0;

            .footer-section {
                flex: 1;
                min-width: 120px;

                .sub-menu-container {
                    .sub-title {
                        display: flex;
                        align-items: center;
                        height: 40px;
                        font-size: 10px;
                        color: rgba(255, 255, 255, 0.57);
                        line-height: 40px;
                        border-top: 1px solid rgba(255, 255, 255, 0.12);
                        margin: 0 16px;

                        &::after {
                            --un-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none"><path d="M16 20H24" stroke="white" stroke-opacity="0.91" stroke-linecap="round"/><path d="M20 16L20 24" stroke="white" stroke-opacity="0.91" stroke-linecap="round"/></svg>') no-repeat;
                            content: "";
                            background-color: rgba(255, 255, 255, 0.91);
                            height: 40px;
                            mask: var(--un-icon) no-repeat;
                            mask-size: 100% 100%;
                            -webkit-mask: var(--un-icon) no-repeat;
                            -webkit-mask-size: 100% 100%;
                            position: absolute;
                            right: 16px;
                            transition: all .3s;
                            width: 40px;
                        }

                        &.no-items::after {
                            --un-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="6" height="10" viewBox="0 0 6 10" fill="none"><path d="M1 9L5 5L1 1" stroke="white" stroke-opacity="0.91" stroke-linecap="round" stroke-linejoin="round"/></svg>') no-repeat;
                            width: 8px;
                            height: 40px;
                            right: 32px;
                        }
                    }

                    .sub-items {
                        height: 0;
                        overflow: hidden;
                        margin: 0;
                        opacity: 0;
                        background: #252525;
                        padding: 0 16px;
                        transition: all 0.3s ease-in-out;
                    }

                    &.active {
                        .sub-title {
                            &::after {
                                --un-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none"><path d="M16 20H24" stroke="white" stroke-opacity="0.91" stroke-linecap="round"/></svg>') no-repeat;
                            }
                        }

                        .sub-items {
                            height: 100%;
                            opacity: 1;
                            padding: 19px 16px;
                        }
                    }

                    .sub-item {
                        height: 18px;
                        color: rgba(255, 255, 255, 0.91);
                        font-size: 10px;
                        line-height: 18px;
                        margin-bottom: 22px;

                        &::after {
                            content: "";
                            display: none;
                        }

                        &:nth-last-child(1) {
                            margin-bottom: 0;
                        }
                    }
                }
            }


        }


        .footer-icons {
            position: initial;
            transform: initial;
            flex-direction: row;

            .social-icons {
                display: flex;
                align-items: center;
                margin: 16px 0;

                .social-link {
                    margin-right: 12px;
                    position: relative;

                    &:hover svg {
                        filter: brightness(3);
                    }

                    .qrcode {
                        opacity: 1;
                        visibility: visible;
                        right: initial;
                    }

                    &:hover .qrcode {
                        opacity: 0;
                        visibility: hidden;
                    }
                }

            }


        }


        .footer-contact {
            display: flex;
            flex-direction: column;
            margin: 0 16px;
            padding: 24px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.12);
            border-bottom: none;
            gap: 0;

            .contact-card {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                background-color: inherit;
                padding: 0;
                border-radius: 0;
                text-align: center;
                width: initial;
                min-height: initial;

                .type-title {
                    color: rgba(255, 255, 255, 0.91);
                    font-family: "PingFang SC";
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 18px;
                    margin-bottom: 8px;
                }

                .desc {
                    display: flex;
                    align-items: center;
                    color: rgba(255, 255, 255, 0.57);
                    font-family: "PingFang SC";
                    font-size: 10px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 15px;
                    margin-bottom: 12px;

                    .desc-text {
                        color: #1677FF;
                        width: initial;
                        height: initial;
                        margin: 0;
                        background: initial;
                    }
                }

                .input {
                    position: relative;
                    width: 100%;

                    input {
                        font-size: 10px;
                        padding: 7px 12px;
                        width: 100%;
                        height: 28px;
                        background: inherit;
                        padding-left: 12px;
                        padding-right: 40px;
                    }

                }
            }
        }

        .footer-copyright {
            color: #6C7073;
            text-align: left;
            font-size: 10px;
            line-height: 15px;
            margin: 0 16px 0;
        }


        .footer-bottom-text {
            width: 100%;
            background: #000;
            aspect-ratio: 1920/422;
            mask-image: url(../assets/imgs/footer_bottom_text.png);
            mask-size: contain;
            mask-repeat: no-repeat;
            image-rendering: crisp-edges;
            position: relative;
            transition: all 1s cubic-bezier(.165, .84, .44, 1);
            transform-origin: bottom;
            overflow: hidden;

            .noise-mask {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url(../assets/imgs/noise.png) repeat;
                z-index: 5;
            }

            .spotlight {
                position: absolute;
                filter: blur(100px);
                width: 70rem;
                height: 70rem;
                aspect-ratio: 1/1;
                border-radius: 100%;
                transition: opacity 1s cubic-bezier(.165, .84, .44, 1);
                background: radial-gradient(circle at center, rgba(255, 255, 255, .2), rgba(255, 255, 255, .1), rgba(255, 255, 255, 0));
                transform: translate3d(calc(var(--mouse-x, -100%) * 1px - 35rem), calc(var(--mouse-y, -100%) * 1px - 35rem), 0);
            }
        }
    }
}

.icon-global {
    --un-icon: url('../assets/imgs/earth.svg') no-repeat;
    background-color: rgba(255, 255, 255, 0.91);
    display: inline-block;
    width: 12px;
    height: 12px;
    mask: var(--un-icon) no-repeat;
    mask-size: 100% 100%;
    -webkit-mask: var(--un-icon) no-repeat;
    -webkit-mask-size: 100% 100%;
}

.icon-phone {
    --un-icon: url('../assets/imgs/icon_phone.svg') no-repeat;
    background-color: rgba(255, 255, 255, 0.91);
    display: inline-block;
    width: 12px;
    height: 12px;
    mask: var(--un-icon) no-repeat;
    mask-size: 100% 100%;
    -webkit-mask: var(--un-icon) no-repeat;
    -webkit-mask-size: 100% 100%;
}

.icon-email {
    --un-icon: url('../assets/imgs/icon_mail.svg') no-repeat;
    background-color: rgba(255, 255, 255, 0.91);
    display: inline-block;
    width: 12px;
    height: 12px;
    mask: var(--un-icon) no-repeat;
    mask-size: 100% 100%;
    -webkit-mask: var(--un-icon) no-repeat;
    -webkit-mask-size: 100% 100%;
}

.icon-up-top {
    --un-icon: url('../assets/imgs/up_top_icon.svg') no-repeat;
    background-color: rgba(255, 255, 255, 0.91);
    display: inline-block;
    width: 12px;
    height: 12px;
    mask: var(--un-icon) no-repeat;
    mask-size: 100% 100%;
    -webkit-mask: var(--un-icon) no-repeat;
    -webkit-mask-size: 100% 100%;
}
</style>
