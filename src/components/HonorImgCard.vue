<template>
    <div class="honor-img-card">
        <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
        <div class="imgs">
            <div class="item" v-for="(item, index) in data.list" :key="index">
                <img :src="$patchRawUrl(item.img.url)" alt="" class="img">
            </div>
        </div>
    </div>
</template>
<script setup lang='ts'>

const props = defineProps({
    data: {
        type: Object,
        default: () => []
    }
})



</script>
<style lang="less" scoped>
.honor-img-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 0 24px;
    margin-bottom: var(--component-mb);

    .title {
        max-width: 816px;
        font-family: "PingFang SC";
        font-size: 48px;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        color: var(--title-theme-color);
        text-align: center;
        margin-bottom: 40px;
    }

    .imgs {
        display: flex;
        max-width: 1440px;
        gap: 24px;
    }

    .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        // width: calc(50vw - 36px);
        // height: 464px;
        position: relative;

        .img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }
}

@media (max-width: 768px) {
    .honor-img-card {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 0;
        margin-bottom: var(--component-mb);

        .title {
            font-size: 18px;
            line-height: 21px;
            margin-bottom: 16px;
        }

        .imgs {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            max-width: 1440px;
            gap: 16px;
        }

        .item {
            width: calc(50vw - 24px);
            border-radius: initial;
        }
    }
}
</style>