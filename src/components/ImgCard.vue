<template>
    <div class="img-card" v-if="data.list">
        <div class="item" v-for="(item, index) in data.list" :key="index">
            <div class="title" :style="{ color: item.textColor || data.textColor || '#fff' }">{{ item.title }}</div>
            <div class="sub-title" :style="{ color: item.textColor || data.textColor || '#fff' }">{{ item.subTitle }}</div>
            <a class="btn-more" :href="item.jumpLink">
                <div class="keep-btn-left"  :style="{ borderColor: item.textColor || data.textColor || '#fff' }">
                    <span class="btn-text" :style="{ color: item.textColor || data.textColor || '#fff' }">了解更多</span>
                     <i class="btn-more-icon icon-right-arrow"
                                :style="{ backgroundColor: item.textColor || data.textColor || '#fff' }"></i>
                </div>
            </a>
            <img :src="patchRawUrl(item.img.url)" alt="" class="img">
        </div>
    </div>
</template>
<script setup lang='ts'>

const patchRawUrl = usePatchRawUrl();

const props = defineProps({
    data: {
        type: Object,
        default: () => []
    }
})



</script>
<style lang="less" scoped>
.img-card {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    padding: 0 24px;
    grid-row-gap: 24px;
    margin-bottom: var(--component-mb);

    .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: calc(50vw - 36px);
        height: 560px;
        border-radius: 12px;
        // margin-bottom: 24px;
        position: relative;
        z-index: 0;
        overflow: hidden;

        .title {
            max-width: 480px;
            text-align: center;
            color: rgba(255, 255, 255, 0.91);
            font-family: "PingFang SC";
            font-size: 40px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 60px;
            margin-top: 40px;
        }

        .sub-title {
            max-width: 600px;
            text-align: center;
            color: rgba(255, 255, 255, 0.91);
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            padding: 0 12px;
            margin-top: 24px;
        }

        .btn-more {
            width: 130px;
            height: 40px;
            z-index: 1;
            margin-top: 24px;

            .keep-btn-left {
                display: flex;
                width: 130px;
                height: 40px;
                padding: 8px 16px;
                justify-content: space-between;
                align-items: center;
                gap: 12px;
                flex-shrink: 0;
                border-radius: 60px;
                border: 1px solid #FFF;
                cursor: pointer;
                position: relative;
                transition: all 0.3s ease-in-out;

                .btn-text {
                    color: #FFF;
                    font-family: "PingFang SC";
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 300;
                    line-height: 24px;
                }

                .btn-more-icon {
                    width: 20px;
                    height: 20px;
                }
            }

            &:hover .keep-btn-left {
                width: 140px;
                background: rgba(255, 255, 255, 0.2);
            }

        }

        .img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -1;
            transition: all .3s ease-in-out;
        }

        &:hover .img {
            transform: scale(1.1);
        }
    }
}

@media (max-width: 768px) {
    .img-card {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 0;
        margin-bottom: var(--component-mb);
        grid-row-gap: 16px;

        .item {
            width: 100%;
            border-radius: initial;
            // margin-bottom: 16px;

            &:hover .img {
                transform: initial;
            }

            .title {
                height: 24px;
                font-size: 16px;
                line-height: 24px;
                margin-top: 40px;
            }

            .sub-title {
                height: 21px;
                font-size: 14px;
                line-height: 21px;
                margin-top: 4px;
            }

            .btn-more {
                height: 28px;
                padding: 8px 12px;
                justify-content: center;
                align-items: center;
                flex-shrink: 0;
                margin-top: 8px;

                .keep-btn-left,
                &:hover .keep-btn-left {
                    width: 110px;
                    height: 28px;
                    padding: 8px 12px;
                    justify-content: center;

                    .btn-text {
                        color: var(--, rgba(255, 255, 255, 0.97));
                        font-family: "PingFang SC";
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 18px;
                    }
                }

            }
        }
    }
}
</style>