<template>
    <div class="img-card2">
        <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
        <div class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</div>
        <div class="img-items " v-if="data.list.length">
            <div class="left">
                <a class="item" :href="data.list[0].jumpLink">
                    <div class="item-title" :style="{ color: data.list[0].textColor || data.textColor || '#fff' }">{{
                        data.list[0].title }}</div>
                    <img :src="$patchRawUrl(data.list[0].resource.url)" alt="" class="img">
                </a>
            </div>
            <div class="right">
                <a class="item" v-for="(item, index) in data.list.slice(1)" :key="index" :href="item.jumpLink">
                    <div class="item-title" :style="{ color: item.textColor || data.textColor || '#fff' }">{{ item.title
                        }}</div>
                    <img :src="$patchRawUrl(item.resource.url)" alt="" class="img">
                </a>
            </div>
        </div>
    </div>
</template>
<script setup lang='ts'>

const props = defineProps({
    data: {
        type: Object,
        default: () => []
    }
})

</script>
<style lang="less" scoped>
.img-card2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-bottom: var(--component-mb);

    .title {
        max-width: 816px;
        text-align: center;
        color: rgba(0, 0, 0, 0.8);
        font-family: "PingFang SC";
        font-size: 48px;
        font-style: normal;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        margin-bottom: 24px;
    }

    .sub-title {
        max-width: 1260px;
        color: rgba(0, 0, 0, 0.65);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: var(--sub-title-font-weight);
        line-height: 36px;
        margin-bottom: 40px;

    }

    .img-items {
        display: flex;
        max-width: 1440px;
        margin: 0 20px;

        .item {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            position: relative;
            z-index: 0;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.1s ease-in-out;

            .item-title {
                color: #fff;
                font-family: "PingFang SC";
                font-size: 32px;
                font-style: normal;
                font-weight: var(--title-font-weight);
                line-height: 48px;
            }

            .img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: absolute;
                z-index: -1;
            }

            &:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            }
        }

        .left {
            width: 464px;
            height: 424px;
            flex: 0 0 auto;

            .item {
                width: 100%;
                height: 100%;
            }
        }

        .right {
            display: flex;
            flex: 1 1 auto;
            flex-wrap: wrap;
            grid-row-gap: 24px;

            .item {
                width: 464px;
                // height: 200px;
                margin-left: 24px;
                // margin-bottom: 24px;
            }
        }


    }
}

@media (max-width: 768px) {
    .img-card2 {
        // margin-bottom: 16px;

        .title {
            font-size: 18px;
            line-height: 27px;
            margin-bottom: 8px;
        }

        .sub-title {
            font-size: 14px;
            line-height: 21px;
            margin-bottom: 12px;
        }

        .img-items {
            width: 100%;
            flex-direction: column;
            margin: 0;

            .item {
                border-radius: 0;

                .item-title {
                    font-size: 16px;
                    line-height: 24px;
                }

                .img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    position: absolute;
                    z-index: -1;
                }

                &:hover {
                    transform: initial;
                    box-shadow: initial;
                }
            }

            .left {
                width: 100%;
                height: 200px;
                flex: 0 0 auto;
                margin-bottom: 16px;

                .item {
                    width: 100%;
                    height: 100%;
                }
            }

            .right {
                display: flex;
                flex: 1 1 auto;
                flex-wrap: wrap;
                grid-row-gap: 16px;

                .item {
                    width: 100%;
                    height: 200px;
                    margin: initial;
                    // margin-bottom: 16px;

                }
            }


        }
    }
}
</style>