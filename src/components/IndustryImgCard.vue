<template>
    <div class="industry-img-card">
        <div class="header">
            <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
            <p class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</p>
        </div>

        <div class="industry-grid">
            <div v-for="(industry, index) in data.list" :key="index" class="industry-card">
                <div class="thumbnail-container">
                    <img :src="$patchRawUrl(industry.thumbnail.url)" alt="预览" class="thumbnail" />
                </div>
                <div class="card-desc">
                    <span class="industry-title">{{ industry.title }}</span>
                    <div class="controls">
                        <a class="watch-text" :href="industry.jumpLink">
                            <span>了解更多</span>
                            <i class="icon-right-arrow"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: []
            }
        }
    }
})

const isMobile = ref(false);



onMounted(() => {
    isMobile.value = window.innerWidth < 768;
})
</script>

<style lang="less" scoped>
.industry-img-card {
    width: 100%;
    padding: 0 20px;
    margin-bottom: var(--component-mb);

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            max-width: 1260px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .industry-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
        max-width: 1440px;
        margin: 0 auto;

        .industry-card {
            // width: 342px;
            // height: 440px;
            aspect-ratio: 464/352;
            border-radius: 8px;
            overflow: hidden;
            // box-shadow: 0px 6px 30px 5px rgba(0, 0, 0, 0.05), 0px 16px 24px 2px rgba(0, 0, 0, 0.04), 0px 8px 10px -5px rgba(0, 0, 0, 0.08);
            cursor: pointer;
            position: relative;

            .thumbnail-container {
                width: 100%;
                height: 100%;
                overflow: hidden;

                .thumbnail {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .card-desc {
                padding: 24px 24px 29px;
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;

                .industry-title {
                    display: flex;
                    color: rgba(255, 255, 255, 0.91);
                    font-family: "PingFang SC";
                    font-size: 24px;
                    font-weight: var(--sub-title-font-weight);
                    line-height: 34px;
                    margin-bottom: 24px;
                }

                .controls {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    height: 21px;

                    .watch-text {
                        display: flex;
                        align-items: center;
                        color: rgba(255, 255, 255, 0.91);
                        font-family: "PingFang SC";
                        font-size: 16px;
                        font-weight: 400;
                        line-height: 22px;
                    }

                    .icon-right-arrow {
                        margin-left: 12px;
                    }
                }
            }

            .more-industry {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0%;
                left: 0%;

                .more-text {
                    color: rgba(255, 255, 255, 0.91);
                    text-align: center;
                    font-family: "PingFang SC";
                    font-size: 24px;
                    font-weight: 400;
                    line-height: 36px;
                    padding: 0 12px;
                    margin-top: -6px;
                    margin-bottom: 24px;
                }

                .more-btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 56px;
                    height: 56px;
                    border-radius: 56px;
                    border: 1px solid #fff;

                    &:hover {
                        background: rgba(255, 255, 255, 0.2);
                    }
                }
            }
        }
    }

    .open-more-btn {
        display: none;
    }
}

@media(max-width: 768px) {
    .industry-img-card {
        padding: 0 16px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }

        .industry-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            justify-content: space-between;

            .industry-card {
                width: 100%;
                aspect-ratio: 361/274;
                border-radius: 6px;

                .card-desc {
                    padding: 16px;

                    .industry-title {
                        font-size: 14px;
                        line-height: 20px;
                        margin-bottom: 0;
                        margin-right: 8px;
                    }

                    .controls {
                        height: 40px;

                        .watch-text {
                            font-size: 12px;
                            line-height: 18px;
                        }

                        .icon-right-arrow {
                            margin-left: 8px;
                            width: 16px;
                            height: 16px;
                        }
                    }
                }

            }
        }

        .open-more-btn {
            display: flex;
            width: 100%;
            height: 40px;
            padding: 12px 32px;
            justify-content: center;
            align-items: center;
            border-radius: 20px;
            background: linear-gradient(180deg, #000 -42.86%, #555 100%);
            color: rgba(255, 255, 255, 0.97);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            text-align: center;
            margin: 24px auto 0;
        }
    }
}
</style>