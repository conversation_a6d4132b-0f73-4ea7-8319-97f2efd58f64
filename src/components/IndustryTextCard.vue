<template>
  <div class="text-card">
    <div class="header">
      <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
    </div>

    <div class="card-container top-cards">
      <div class="card-item" v-for="(item, index) in data.cards" :key="index">
        <img :src="$patchRawUrl(item.img.url)" alt="卡片图标" class="card-icon" />
        <div class="card-title">{{ item.title }}</div>
        <div class="card-content">{{ item.desc }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        title: '',
        desc: '',
        cards: [],
      }
    }
  }
})

</script>

<style scoped lang="less">
.text-card {
  width: 100%;
  padding: 0 20px 100px;
  margin-bottom: var(--component-mb);
  background-color: #eee;

  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 40px;
    padding-top: 100px;

    .title {
      max-width: 816px;
      text-align: center;
      color: rgba(0, 0, 0, 0.8);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 48px;
      font-style: normal;
      font-weight: var(--title-font-weight);
      line-height: 72px;
    }
  }

  .card-container {
    display: flex;
    gap: 24px;
    // min-width: 1200px;
    max-width: 1440px;
    margin: 0 auto 40px;

    .card-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 464px;
      max-width: 464px;
      aspect-ratio: 464/347;
      border-radius: 12px;
      background: #FFF;
      border-radius: 12px;
      padding: 0 34px 24px;

      .card-icon {
        width: 120px;
        aspect-ratio: 1/1;
        margin: 40px auto 27px;
        object-fit: contain;
      }

      .card-title {
        color: rgba(0, 0, 0, 0.80);
        font-family: "PingFang SC";
        font-size: 28px;
        font-weight: var(--title-font-weight);
        line-height: 42px;
        margin-bottom: 12px;
      }

      .card-content {
        color: rgba(0, 0, 0, 0.65);
        font-family: "PingFang SC";
        font-size: 18px;
        font-weight: 400;
        line-height: 27px;
      }
    }
  }
}

@media (max-width: 768px) {
  .text-card {
    padding: 0 16px 32px;

    .header {
      text-align: center;
      margin-bottom: 12px;
      padding-top: 32px;

      .title {
        font-size: 18px;
        line-height: 21px;
      }
    }

    .card-container {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 0;

      .card-item {
        width: 100%;
        max-width: 100%;
        aspect-ratio: initial;
        border-radius: 6px;
        background: #FFF;
        padding: 12px;
        text-align: center;

        .card-icon {
          width: 32px;
          aspect-ratio: 1/1;
          margin: 0 auto 12px;
          object-fit: contain;
        }

        .card-title {
          font-size: 14px;
          line-height: 21px;
          margin-bottom: 4px;
        }

        .card-content {
          font-size: 12px;
          line-height: 18px;
        }
      }
    }
  }
}
</style>