<template>
    <div class="intro">
        <div class="top-block">
            <div class="left-title">{{ data.title }}</div>
            <div class="right-content">
                <div class="desc-title">{{ data.descTitle }}</div>
                <div class="desc-content" v-html="data.descContent">

                </div>
            </div>
        </div>
        <div class="bottom-block" data-aos="fade-up" data-aos-delay="300">
            <div class="item" v-for="(item, index) in data.numberItems" :key="index">
                <span class="countup item-value" :data-countup-val="item.value">{{ item.value }}</span>
                <span class="item-key">{{ item.key }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { CountUp } from 'countup.js';

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                title: '',
                descTitle: '',
                descContent: '',
                numberItems: []
            }
        }
    }
})

useAos();

const countups = ref<CountUp[]>([]);
const playCountup = () => {
    document.querySelectorAll('.countup').forEach((el) => {
        const val = parseInt((el as HTMLSpanElement).dataset.countupVal);
        console.log(val);
        const countup = new CountUp(el as HTMLElement, val, {
            enableScrollSpy: true,
            scrollSpyDelay: 300,
            formattingFn: (num) => {
                return num + '+';
            },
        });

        countups.value.push(countup);
    });
};
const initPage = () => {
    playCountup();
};

useDelayMounted(initPage);

</script>

<style scoped lang="less">
@font-face {
    font-family: DIN-Pro;
    src: url(../assets/fonts/DINPro-Black.otf);
}

.intro {
    width: 100%;
    padding: 100px 20px 100px;
    margin-bottom: var(--component-mb);
    background-color: #fff;

    .top-block {
        max-width: 1440px;
        display: flex;
        justify-content: space-between;
        margin: 0 auto 56px;

        .left-title {
            width: 488px;
            color: #DF0428;
            font-family: "PingFang SC";
            font-size: 48px;
            font-weight: var(--title-font-weight);
            line-height: 72px;
            padding-right: 20px;
            flex: 1 1 auto;
        }

        .right-content {
            width: 952px;

            .desc-title {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 32px;
                font-weight: var(--title-font-weight);
                line-height: 48px;
                margin-bottom: 24px;
            }

            .desc-content {
                color: rgba(0, 0, 0, 0.65);
                font-family: "PingFang SC";
                font-size: 24px;
                font-style: normal;
                font-weight: 300;
                line-height: 36px;
            }
        }
    }

    .bottom-block {
        max-width: 1440px;
        display: flex;
        justify-content: space-between;
        margin: 0 auto;

        .item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .item-key {
                color: rgba(0, 0, 0, 0.65);
                font-family: "PingFang SC";
                font-size: 18px;
                font-weight: 400;
                line-height: 27px;
            }

            .item-value {
                font-family: DIN-Pro;
                color: rgba(0, 0, 0, 0.80);
                font-size: 50px;
                font-weight: 500;
                line-height: 61px;
                margin-bottom: 24px;
            }
        }
    }

    &:deep(p) {
        margin-bottom: 24px;
    }
}

@media (max-width: 768px) {
    .intro {
        padding: 0 16px 32px;

        .top-block {
            padding-top: 30px;
            flex-direction: column;
            margin-bottom: 8px;

            .left-title {
                width: 100%;
                font-size: 18px;
                line-height: 21px;
                margin-bottom: 12px;
                text-align: center;
            }

            .right-content {
                width: 100%;

                .desc-title {
                    color: rgba(0, 0, 0, 0.65);
                    font-size: 14px;
                    font-weight: var(--title-font-weight);
                    line-height: 21px;
                    margin-bottom: 16px;
                }

                .desc-content {
                    color: rgba(0, 0, 0, 0.65);
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 300;
                    line-height: 21px;
                }
            }
        }

        .bottom-block {
            .item {
                .item-key {
                    font-size: 14px;
                    line-height: 21px;
                }

                .item-value {
                    font-family: DIN-Pro;
                    font-size: 18px;
                    line-height: 21px;
                    margin-bottom: 12px;
                }
            }
        }

        &:deep(p) {
            margin-bottom: 16px;
        }
    }


}
</style>