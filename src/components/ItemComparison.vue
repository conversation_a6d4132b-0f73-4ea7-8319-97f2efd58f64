<template>
    <div class="item-comparison">
        <h1 class="title" :style="{ color: data.textColor }">{{ data.title }}</h1>
        <p class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</p>
        <div class="image-container">
            <div class="img-item trapezoid left" :class="{ active: activeImg === 0 }" @mouseenter="activeImg = 0">
                <div class="item-name">{{ data.imgs[0].name }}</div>
                <img :src="$patchRawUrl(data.imgs[0].url.url)" alt="左物品" class="img" />
            </div>
            <div class="img-item trapezoid right" :class="{ active: activeImg === 1 }" @mouseenter="activeImg = 1">
                <div class="item-name">{{ data.imgs[1].name }}</div>
                <img :src="$patchRawUrl(data.imgs[1].url.url)" alt="右物品" class="img" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: []
            }
        }
    }
});

const activeImg = ref(1);

</script>

<style lang="less" scoped>
.item-comparison {
    max-width: 1440px;
    margin: 0 auto;
    margin-bottom: var(--component-mb);

    .title {
        max-width: 816px;
        font-family: "PingFang SC";
        font-size: 40px;
        font-weight: var(--title-font-weight);
        line-height: 56px;
        text-align: center;
        margin-bottom: 24px;
        margin: 0 auto 24px;
    }

    .sub-title {
        max-width: 1260px;
        font-family: "PingFang SC";
        font-size: 24px;
        font-weight: 400;
        line-height: 36px;
        text-align: center;
        margin-bottom: 40px;
        margin: 0 auto 40px;
    }

    .image-container {
        display: flex;
        width: 100%;
        aspect-ratio: 1440/435;
        position: relative;
        transform-origin: bottom;

        .trapezoid {
            flex: 0 0 auto;
            height: 100%;
            transition: all .3s ease;
            animation-fill-mode: both;
            animation-duration: 800ms;
            overflow: hidden;

            .item-name {
                display: inline-flex;
                height: 38px;
                padding: 0 24px;
                align-items: center;
                flex-shrink: 0;
                border-radius: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: #FFF;
                font-family: "PingFang SC";
                font-size: 18px;
                font-weight: 400;
                position: absolute;
                top: 28px;
            }

            .img {
                object-fit: cover;
                width: 100%;
                height: 100%;
            }
        }

        .left {
            // width: 571px;
            height: 56%;
            aspect-ratio: 571/242;
            clip-path: polygon(0 0, 100% 0, calc(100% - 20.3%) 100%, 0 100%);
            // mask-image: url("../assets/imgs/shape_T_left.svg");
            // mask-repeat: no-repeat;
            // mask-size: 100% 100%;
            border-top-left-radius: 16px;
            border-bottom-left-radius: 16px;
            position: absolute;
            left: 0;
            bottom: 0;
            z-index: 1;

            &.active {
                height: 100%;
                aspect-ratio: 1060/435;
                animation-name: bounce;
                clip-path: polygon(0 0, 100% 0, calc(100% - 21.5%) 100%, 0 100%);
                z-index: 2;
            }

            .item-name {
                left: 28px;
            }
        }

        .right {
            // width: 571px;
            height: 56%;
            aspect-ratio: 571/242;
            clip-path: polygon(22.1% 0, 100% 0, 100% 100%, 0 100%);
            // mask-image: url("../assets/imgs/shape_T_right.svg");
            // mask-repeat: no-repeat;
            // mask-size: cover;
            border-top-right-radius: 16px;
            border-bottom-right-radius: 16px;
            position: absolute;
            right: 0;
            bottom: 0;
            z-index: 1;

            &.active {
                // width: 951px;
                height: 100%;
                aspect-ratio: 951/435;
                animation-name: bounce;
                clip-path: polygon(22% 0, 100% 0, 100% 100%, 0 100%);
                z-index: 2;
            }

            .item-name {
                right: 54px;
            }
        }
    }

}

@media (max-width: 768px) {

    .item-comparison {
        width: calc(100% - 32px);

        .title{
            font-size: 18px;
            line-height: 27px;
            margin-bottom: 8px;
        }

        .sub-title{
            font-size: 14px;
            line-height: 21px;
            margin-bottom: 12px;
        }

        .image-container {
            flex-direction: column;
            height: auto;
            aspect-ratio: 361/107;

            .left {
                width: initial;
                height: 60%;
                aspect-ratio: 159/67;
                clip-path: polygon(0 0, 100% 0, calc(100% - 26%) 100%, 0 100%);
                border-top-left-radius: 16px;
                border-bottom-left-radius: 16px;
                position: absolute;
                left: 0;
                bottom: 0;
                z-index: 1;

                &.active {
                    width: initial;
                    height: 100%;
                    aspect-ratio: 267/107;
                    animation-name: bounce;
                    clip-path: polygon(0 0, 100% 0, calc(100% - 25%) 100%, 0 100%);
                    z-index: 2;
                }

                .item-name {
                    top: 8px;
                    left: 8px;
                }
            }

            .right {
                width: initial;
                height: 60%;
                aspect-ratio: 159/67;
                clip-path: polygon(26% 0, 100% 0, 100% 100%, 0 100%);
                // mask-image: url("../assets/imgs/shape_T_right.svg");
                // mask-repeat: no-repeat;
                // mask-size: cover;
                border-top-right-radius: 16px;
                border-bottom-right-radius: 16px;
                position: absolute;
                right: 0;
                bottom: 0;
                z-index: 1;

                &.active {
                    width: initial;
                    height: 100%;
                    aspect-ratio: 240/107;
                    animation-name: bounce;
                    clip-path: polygon(27.5% 0, 100% 0, 100% 100%, 0 100%);
                    z-index: 2;
                }

                .item-name {
                    top: 8px;
                    right: 8px;
                }
            }

            .trapezoid .item-name{
                height: 20px;
                padding: 0 8px;
                border-radius: 4px;
                font-size: 12px;
                line-height: 18px;

            }
        }

        .trapezoid {
            clip-path: none;
            height: 300px;
        }
    }
}

@keyframes bounce {
    0% {
        transform: scale(1);
    }

    40% {
        transform: scale(1.08);
    }

    60% {
        transform: scale(0.95);
    }

    80% {
        transform: scale(1.03);
    }

    100% {
        transform: scale(1);
    }
}

.trapezoid {
    animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 1.5);
}
</style>