<template>
    <div class="metric-display-group">
        <div class="header">
            <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
            <div class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</div>
        </div>
        <div class="metrics-container">
            <div class="metric-item" v-for="(item, index) in data.list" :key="index">
                <p class="metric-value">{{ item.value }}</p>
                <p class="metric-label">{{ item.key }}</p>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: []
            }
        }
    }
})

</script>

<style lang="less" scoped>
.metric-display-group {
    width: 100%;
    padding: 0 20px;
    margin-bottom: var(--component-mb);

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            max-width: 1260px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .metrics-container {
        max-width: 1440px;
        display: flex;
        justify-content: center;
        gap: 24px;
        margin: auto;

        .metric-item {
            width: 342px;
            aspect-ratio: 342/200;
            background: #fff;
            border-radius: 12px;
            padding: 40px 24px;
            text-align: center;

            .metric-value {
                font-size: 48px;
                font-weight: 700;
                line-height: 72px;
                margin-bottom: 12px;
                background: linear-gradient(180deg, #8BBBFF 0%, var(--link-, #1677FF) 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .metric-label {
                color: rgba(0, 0, 0, 0.65);
                text-align: center;
                font-family: "PingFang SC";
                font-size: 24px;
                font-weight: 400;
                line-height: 36px;
            }
        }
    }
}

@media (max-width: 768px) {
    .metric-display-group {
        padding: 0 16px 32px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }

        .metrics-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;

            .metric-item {
                width: auto;
                height: 84px;
                aspect-ratio: initial;
                border-radius: 12px;
                padding: 16px;
                text-align: center;

                .metric-value {
                    font-size: 18px;
                    line-height: 27px;
                    margin-bottom: 4px;
                }

                .metric-label {
                    font-size: 14px;
                    line-height: 21px;
                }
            }
        }
    }
}
</style>