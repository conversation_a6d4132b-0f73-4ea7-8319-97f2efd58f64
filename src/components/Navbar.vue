<template>
    <div class="nav-bar">
        <ul class="nav-bar-nav" v-show="!searchInputVisible">
            <template v-if="!isMobile">
                <li class="nav-item" :class="{ active: routeIndex == undefined }">
                    <a class="nav-bar-link" href="/">首页</a>
                </li>
                <li class="nav-item pc" @mouseenter="selItem(index)" :class="{ active: curMenuIndex == index , 'route-active': routeIndex == index }"
                    v-for="(menu, index) in menus" :key="index">
                    <div class="nav-bar-link dropdown" v-if="menu.subMenu.length">{{ menu.name }}</div>
                    <a class="nav-bar-link" v-else :href="menu.path+'?mi='+index+'&rn='+menu.name">{{ menu.name }}</a>
                    <template v-if="menu.subMenu.length">
                        <div class="dropdown-container" v-if="menu.subMenu[0].children.length"
                            :class="{ active: curMenuIndex == index, 'no-left-menu': menu.subMenu.length <= 1 }"
                            @mouseleave="selItem(-1)">
                            <div class="dropdown-card  anim-item">
                                <div class="dropdown-card-content">
                                    <div class="menu-left" v-if="menu.subMenu && menu.subMenu.length > 1">
                                        <div class="menu-title">{{ menu.name }}</div>
                                        <ul class="menu-items">
                                            <li class="menu-item" :class="{ active: curSubMenuIndex == subIndex }"
                                                v-for="(subMenu, subIndex) in menu.subMenu" :key="subIndex"
                                                @click="selSubMenu(subIndex)">
                                                <span class="menu-link">{{ subMenu.name }}</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="menu-right" v-if="menu.subMenu[curSubMenuIndex]">
                                        <a :href="cardItem.path+'?mi='+index+'&rn='+cardItem.name" class="menu-content-card"
                                            v-for="(cardItem, cardIndex) in menu.subMenu[curSubMenuIndex].children"
                                            :key="cardIndex">
                                            <div class="card-imgs" v-if="cardItem.imgs.length > 1">
                                                <img :src="$patchRawUrl(cardItem.imgs[0].img.url)" alt=""
                                                    class="card-img img-1">
                                                <img :src="$patchRawUrl(cardItem.imgs[1].img.url)" alt=""
                                                    class="card-img img-2">
                                            </div>
                                            <div class="card-imgs" v-else>
                                                <img :src="$patchRawUrl(cardItem.imgs[0].img.url)" alt=""
                                                    class="card-img">
                                            </div>
                                            <div class="card-text">{{ cardItem.name }}</div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown-container2" v-else :class="{ active: curMenuIndex == index }"
                            @mouseleave="selItem(-1)">
                            <div class="dropdown-card anim-item">
                                <a :href="subMenu.path+'?mi='+index+'&rn='+subMenu.name" class="menu-items" v-for="(subMenu, subIndex) in menu.subMenu"
                                    :key="subIndex">
                                    <div class="menu-item">{{
                                        subMenu.name }}</div>
                                </a>
                            </div>
                        </div>
                    </template>
                </li>
            </template>
            <template v-if="isMobile">
                <li class="nav-item">
                    <a class="nav-bar-link" href="/">首页</a>
                </li>
                <li class="nav-item" @click="selItem(index)" :class="{ active: curMenuIndex == index }"
                    v-for="(menu, index) in mobileMenus" :key="index">
                    <div class="nav-bar-link dropdown" v-if="menu.subMenu.length">{{ menu.name }}</div>
                    <a class="nav-bar-link" v-else :href="menu.path+'?mi='+index+'&rn='+menu.name">{{ menu.name }}</a>
                    <template v-if="menu.subMenu.length">

                        <div class="dropdown-container" @click.stop="" v-if="menu.subMenu[0].children.length"
                            :class="{ active: curMenuIndex == index, 'no-left-menu': menu.subMenu.length <= 1 }">
                            <div class="dropdown-card  anim-item">
                                <div class="dropdown-card-content">
                                    <div class="menu-left" v-if="menu.subMenu && menu.subMenu.length > 1">
                                        <div class="menu-title">{{ menu.name }}</div>
                                        <ul class="menu-items">
                                            <li class="menu-item" :class="{ active: curSubMenuIndex == subIndex }"
                                                v-for="(subMenu, subIndex) in menu.subMenu" :key="subIndex"
                                                @click="selSubMenu(subIndex)">
                                                <span class="menu-link">{{ subMenu.name }}</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="menu-right" v-if="menu.subMenu[curSubMenuIndex]">
                                        <a :href="cardItem.path+'?mi='+index+'&rn='+cardItem.name" class="menu-content-card"
                                            v-for="(cardItem, cardIndex) in menu.subMenu[curSubMenuIndex].children"
                                            :key="cardIndex">
                                            <div class="card-imgs" v-if="cardItem.imgs.length > 1">
                                                <img :src="$patchRawUrl(cardItem.imgs[0].img.url)" alt=""
                                                    class="card-img img-1">
                                                <img :src="$patchRawUrl(cardItem.imgs[1].img.url)" alt=""
                                                    class="card-img img-2">
                                            </div>
                                            <div class="card-imgs" v-else>
                                                <img :src="$patchRawUrl(cardItem.imgs[0].img.url)" alt=""
                                                    class="card-img">
                                            </div>
                                            <div class="card-text">{{ cardItem.name }}</div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown-container2" @click.stop="" v-else
                            :class="{ active: curMenuIndex == index }">
                            <div class="menu-items anim-item">
                                <a :href="subMenu.path+'?mi='+index+'&rn='+subMenu.name" class="menu-item" v-for="(subMenu, subIndex) in menu.subMenu"
                                    :key="subIndex">{{
                                        subMenu.name }}</a>
                            </div>
                        </div>
                    </template>
                </li>
            </template>
        </ul>
        <i class="icon-navbar-toggler" v-show="isMobile && !searchInputVisible" @click="navbarToggle"></i>
        <div class="search-container pc" :class="{ 'active': searchInputVisible }" v-if="!isMobile">
            <i class="search-icon" @click="showSearchInput"></i>
            <input class="search-input" placeholder="搜索" v-model="searchText" />
            <i class="search-cancel-icon" @click="hideSearchInput"></i>
            <div class="search-preview-container" :class="{ 'active': searchInputVisible && searchText }">
                <div class="preview-card">
                    <div class="card-title">猜你想搜</div>
                    <div class="preview-items" v-for="(item, index) in prevSearchItems" :key="index">
                        <!-- <div class="item" @click="search(item.path)">{{ item.name }}</div> -->
                         <div class="item" @click="linkTo(item.path)">{{ item.name }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="search-container mobile" :class="{ 'active': searchInputVisible }" v-else>
            <input class="search-input" type="search" placeholder="搜索" v-model="searchText" @keyup.enter="handleSearch"
                @keyup.native.enter="handleSearch" />
            <i class="search-icon" @click="showSearchInput" v-if="!searchInputVisible"></i>
            <i class="search-icon" @click="handleSearch" v-if="searchInputVisible"></i>
            <i class="search-cancel-icon" @click="hideSearchInput"></i>
            <div class="search-preview-container" :class="{ 'active': searchInputVisible }" @touchmove.prevent
                @wheel.stop>
                <div class="preview-card">
                    <template v-if="!searchText">
                        <div class="card-title">快速导航</div>
                        <div class="preview-items" v-for="(item, index) in quicklyData" :key="index">
                            <div class="item" @click="linkTo(item.path)">{{ item.name }}</div>
                        </div>
                    </template>
                    <template v-if="searchText && prevSearchMoel">
                        <div class="card-title">搜索结果</div>
                        <div class="preview-items" v-for="(item, index) in prevSearchItems" :key="index">
                            <div class="item" @click="linkTo(item.path)">{{ item.name }}</div>
                        </div>
                    </template>
                    <template v-if="searchText && realSearchModel">
                        <div class="search-res-container">
                            <div class="type-tabs">
                                <div class="tab-item" :class="{ active: resType == 'product' }"
                                    @click="resType = 'product'">产品（{{ searchResData.product.length }}）</div>
                                <div class="tab-item" :class="{ active: resType == 'news' }" @click="resType = 'news'">
                                    产品新闻（{{ searchResData.news.length }}）</div>
                            </div>
                            <div class="search-res-content" v-if="resType == 'product'">
                                <div class="res-item" v-for="(item, index) in searchResData.product" :key="index">
                                    <div class="content-left" v-if="item.poster">
                                        <img :src="item.poster" alt="" class="content-img">
                                    </div>
                                    <div class="content-right">
                                        <div class="content-name">{{ item.name }}</div>
                                        <div class="content-title">{{ item.title }}</div>
                                        <div class="content-link" @click="linkTo(item.path)">
                                            <span class="link-text">进一步了解</span>
                                            <i class="link-icon"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="search-res-content" v-if="resType == 'news'">
                                <div class="res-item" @click="showNewsModal(item.content)"
                                    v-for="(item, index) in searchResData.news" :key="index">
                                    <div class="content-left" v-if="item.poster">
                                        <img :src="$patchRawUrl(item.poster)" alt="" class="content-img">
                                    </div>
                                    <div class="content-right">
                                        <div class="content-name">{{ item.name }}</div>
                                        <div class="content-title">{{ item.subTitle }}</div>
                                        <div class="content-link">
                                            <span class="news-text">新闻</span>
                                            <span class="news-time">{{ formatTime(item.created_at, 'yyyy-MM-dd')
                                                }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <a v-show="!searchInputVisible || !isMobile"
            :href="shopUrl+'?mi=shop'"
            target="_blank" class="shop-link">
            <i class="shop-link-icon"></i>
            <span class="shop-link-text">淘宝店铺</span>
        </a>
        <div class="news-modal" v-if="showNews">
            <div class="top-btns">
                <i class="icon-close" @click="hideNewsModal"></i>
            </div>
            <!-- <iframe class="news-iframe" src="https://v.xiumi.us/board/v5/4YYNe/617557084" frameborder="0"></iframe> -->
             <div v-html="newsContent"></div>
        </div>
    </div>
</template>

<script setup>
const route = useRoute()
const routeIndex = ref(route.query.mi);
console.log(route.query.mi);
import { formatTime } from '../utils';
const config = useRuntimeConfig();
const baseURL = config.public.NUXT_API_BASE;


const isMobile = ref(false);
onMounted(() => {
    isMobile.value = window.innerWidth <= 768;
})


const { data: menuData } = await useAsyncData(
    () => $fetch(baseURL + '/components/component-prop-data-by-name?name=Navbar')
)

const menus = menuData.value.data.list;
const enUrl = menuData.value.data.enUrl;
const shopUrl = menuData.value.data.shopUrl;


const mobileMenus = [];
//把menus里面的subMenu放到menus里面,如果有subMenu,就把subMenu放到mobileMenus里面
for (let i = 0; i < menus.length; i++) {
    if (menus[i].subMenu && menus[i].subMenu.length > 1) {
        if (menus[i].subMenu[0].children && menus[i].subMenu[0].children.length > 0) {
            menus[i].subMenu.forEach((item) => {
                let tmpItem = {};
                tmpItem.name = menus[i].name + '-' + item.name;
                tmpItem.path = item.path;
                tmpItem.subMenu = [item]
                mobileMenus.push(tmpItem);
            })
        } else {
            mobileMenus.push(menus[i]);
        }
    } else {
        mobileMenus.push(menus[i]);
    }
}

// const subMenu = 
const quicklyData = ref([
  {
    name: 'BUNKER MINI',
    path: '/',
  },
  {
    name: 'RANGER MINI3.0',
    path: '/',
  },
  {
    name: 'COBOT MAGIC',
    path: '/',
  },
  {
    name: 'PiPER',
    path: '/',
  }
])
const prevSearchItems = ref([])


const curMenuIndex = ref(-1);
const curSubMenuIndex = ref(0);
const searchInputVisible = ref(false);
const searchText = ref('');
const searchResData = ref({
    product: [],
    news: []
});
const prevSearchMoel = ref(false);
const realSearchModel = ref(false);
const resType = ref('product');
let timeoutId = null;
const showNews = ref(false);
const newsContent = ref('');

const handleQuicklySearch = async () => {
    prevSearchMoel.value = true;
    realSearchModel.value = false;
    if (searchText.value) {
        try {
            // 调用搜索接口
            const config = useRuntimeConfig();
            const baseURL = config.public.NUXT_API_BASE;
            const response = await $fetch(`${baseURL}/sites/search?keyword=${encodeURIComponent(searchText.value)}`);
            
            // 处理接口返回的数据
            if (response && response.data) {
                const result = response.data;
                prevSearchItems.value = result.filter(item => item.type == 'product');
            } else {
                // 搜索不到时不回退到虚拟数据，清空搜索结果
                prevSearchItems.value = [];
            }
        } catch (error) {
            // 出错时不回退到虚拟数据，清空搜索结果
            prevSearchItems.value = [];
        }

    }
    // 这里后续可以接入搜索接口
};

const handleSearch = async () => {
    // 使用接口/sites/search?keyword=进行搜索
    if (searchText.value) {
        try {
            prevSearchMoel.value = false;
            realSearchModel.value = true;
            
            // 调用搜索接口
            const config = useRuntimeConfig();
            const baseURL = config.public.NUXT_API_BASE;
            const response = await $fetch(`${baseURL}/sites/search?keyword=${encodeURIComponent(searchText.value)}`);
            
            // 处理接口返回的数据
            if (response && response.data) {
                const result = response.data;
                searchResData.value.product = result.filter(item => item.type == 'product');
                searchResData.value.news = result.filter(item => item.type == 'news');
            } else {
                // 搜索不到时不回退到虚拟数据，清空搜索结果
                searchResData.value.product = [];
                searchResData.value.news = [];
            }
            
        } catch (error) {
            // 出错时不回退到虚拟数据，清空搜索结果
            searchResData.value.product = [];
            searchResData.value.news = [];
        }
    }
}

watch(searchText, (newVal) => {
    if (timeoutId) {
        clearTimeout(timeoutId);
    }
    timeoutId = window.setTimeout(() => {
        handleQuicklySearch();
    }, 500);
});

onUnmounted(() => {
    if (timeoutId) {
        clearTimeout(timeoutId);
    }
});
const selItem = (index) => {
    if (isMobile.value && index == curMenuIndex.value) {
        curMenuIndex.value = -1;
        return;
    }
    curMenuIndex.value = index;
    if (index != -1) {
        selSubMenu(0);
    }

}

const selSubMenu = (index) => {
    curSubMenuIndex.value = index;
}

const showSearchInput = async () => {
  document.querySelector('.icon-navbar-toggler').classList.remove('active');
  document.querySelector('.nav-bar-nav').classList.remove('active');
  searchInputVisible.value = true;
  
  // 当用户点开搜索框时，自动通过接口获取前5个产品
  try {
    const config = useRuntimeConfig();
    const baseURL = config.public.NUXT_API_BASE;
    const response = await $fetch(`${baseURL}/sites/search?keyword=`);
    
    if (response && response.data) {
      const products = response.data
        .filter(item => item.type === 'product')
        .slice(0, 5)
        .map(item => ({
          name: item.name,
          path: item.path || '/'
        }));
      
      if (products.length > 0) {
        quicklyData.value = products;
      }
    }
  } catch (error) {
    // 出错时保留原来的虚拟数据
  }
}

const hideSearchInput = () => {
    // document.querySelector('.icon-navbar-toggler').classList.remove('active');
    searchInputVisible.value = false;
    searchText.value = '';
}
const search = (item) => {
    hideSearchInput();
}
const navbarToggle = (e) => {
    if (window.innerWidth >= 768) return;

    const el = e.target;
    el.classList.toggle('active');
    el.closest('.nav-bar').querySelector('.nav-bar-nav').classList.toggle('active');
};

const showNewsModal = (html) => {
    showNews.value = true;
    newsContent.value = html;
}
const hideNewsModal = () => {
    showNews.value = false;
}

const linkTo = (url) => {
    window.open(url, '_blank');
    // hideSearchInput();
}
</script>

<style lang="less" scoped>
.nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
    // width: 75%;
    min-width: 1440px;
    height: 80px;
    background: url('../assets/imgs/logo_cn.png') no-repeat left 44px center, rgba(255, 255, 255, 0.50);
    background-size: 120px 37px;
    backdrop-filter: blur(7.75px);
    border-radius: 100px;
    border: 1px solid #FFF;
    position: fixed;
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
    padding-left: 208px;

    .nav-bar-nav {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex: 0 0 auto;
        height: 100%;
        margin: 0;
        padding: 0;

        .nav-item {
            display: inline-flex;
            height: 64px;
            padding: 10px 12px;
            justify-content: center;
            align-items: center;
            margin-right: 8px;
            flex-shrink: 0;

            .nav-bar-link {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                color: #252525;
                font-family: "PingFang SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 300;
                cursor: pointer;

                &.dropdown::after {
                    content: '';
                    background-color: #000;
                    display: block;
                    width: 20px;
                    height: 20px;
                    --un-icon: url('../assets/imgs/chevron-down.svg');
                    mask: var(--un-icon) no-repeat;
                    mask-size: 100% 100%;
                    margin-left: 4px;
                    transition: all 0.3s ease-in-out;
                }

                // &.router-link-active {
                //     color: #DF0428;
                //     font-weight: bold;
                // }
            }

            &.active,
            &.pc:hover {
                color: #DF0428;
                font-family: "PingFang SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 500;
                border-radius: 12px;
                background: #FFF;

                .nav-bar-link {
                    color: #DF0428;
                    font-weight: bold;

                    &.dropdown::after {
                        background-color: #DF0428;
                        transform: rotate(180deg);
                    }
                }
            }

            &.route-active{
                color: #DF0428;
                font-family: "PingFang SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 500;
                border-radius: 12px;
                background: #FFF;

                .nav-bar-link {
                    color: #DF0428;
                    font-weight: bold;

                    &.dropdown::after {
                        background-color: #DF0428;
                        // transform: rotate(180deg);
                    }
                }
            }

            .dropdown-container {
                width: 1200px;
                height: 464px;
                flex-shrink: 0;
                position: absolute;
                left: 50%;
                top: 100%;
                transform: translate(-50%, 0);
                opacity: 0;
                visibility: hidden;
                margin-top: 12px;

                &.no-left-menu {
                    width: 984px;
                    height: 420px;

                    .dropdown-card .dropdown-card-content .menu-right {
                        padding-top: 32px;
                    }
                }

                .dropdown-card {
                    width: 100%;
                    height: 100%;
                    border-radius: 12px;
                    background: #EEE;
                    box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05), 0px 8px 10px 1px rgba(0, 0, 0, 0.06), 0px 5px 5px -3px rgba(0, 0, 0, 0.10);
                    padding: 12px;

                    .dropdown-card-content {
                        display: flex;
                        flex-direction: row;
                        width: 100%;
                        height: 100%;
                        border-radius: 8px;
                        background: #FFF;

                        .menu-left {
                            display: flex;
                            flex-direction: column;
                            width: 218px;
                            height: 100%;
                            flex-shrink: 0;
                            padding-top: 32px;
                            padding-left: 32px;

                            .menu-title {
                                color: rgba(0, 0, 0, 0.80);
                                font-family: "PingFang SC";
                                font-size: 18px;
                                font-style: normal;
                                font-weight: bold;
                                line-height: 27px;
                                flex-shrink: 0;
                                margin-bottom: 16px;
                            }

                            .menu-items {
                                display: flex;
                                flex-direction: column;
                                width: 100%;
                                height: 100%;
                                padding-left: 0;

                                .menu-item {
                                    display: flex;
                                    align-items: center;
                                    width: 156px;
                                    height: 32px;
                                    flex-shrink: 0;
                                    list-style: none;
                                    font-family: "PingFang SC";
                                    color: rgba(0, 0, 0, 0.80);
                                    font-size: 14px;
                                    font-style: normal;
                                    font-weight: 400;
                                    line-height: normal;
                                    margin-bottom: 12px;
                                    padding: 0 18px 0;
                                    cursor: pointer;
                                    transition: all 0.2s ease-in-out;

                                    &::after {
                                        content: "";
                                        display: block;
                                        background: url('../assets/imgs/icon_arrow.svg') no-repeat right center;
                                        background-size: 20px 20px;
                                        width: 20px;
                                        height: 20px;
                                        margin-left: auto;
                                    }

                                    &:hover,
                                    &.active {
                                        width: 186px;
                                        color: #fff;
                                        background-color: #DF0428;
                                        border-radius: 18px;
                                        font-family: "PingFang SC";
                                        font-size: 14px;

                                        &::after {
                                            filter: brightness(0) invert(1);
                                        }
                                    }
                                }
                            }
                        }

                        .menu-right {
                            display: flex;
                            padding-top: 75px;
                            flex-wrap: wrap;
                            overflow: auto;
                            margin-left: 32px;

                            .menu-content-card {
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                width: 282px;
                                height: 330px;
                                flex-shrink: 0;
                                border-radius: 12px;
                                background: #F3F3F3;
                                margin-right: 24px;
                                margin-bottom: 24px;
                                cursor: pointer;

                                .card-imgs {
                                    width: 100%;
                                    height: 260px;
                                    flex-shrink: 0;
                                    position: relative;

                                    .card-img {
                                        width: 100%;
                                        height: 100%;
                                        padding: 24px;
                                        object-fit: contain;
                                        transition: all 0.3s ease-in-out;
                                        position: absolute;
                                        top: 0;
                                        left: 0;
                                    }

                                    .img-1 {
                                        opacity: 1;
                                    }

                                    .img-2 {
                                        opacity: 0;
                                    }

                                    &:hover {
                                        .img-1 {
                                            opacity: 0;
                                        }

                                        .img-2 {
                                            opacity: 1;
                                        }
                                    }
                                }

                                .card-text {
                                    color: rgba(0, 0, 0, 0.80);
                                    text-align: center;
                                    font-family: "PingFang SC";
                                    font-size: 16px;
                                    font-style: normal;
                                    font-weight: 500;
                                    line-height: normal;
                                    margin: 24px 0;
                                }
                            }
                        }
                    }
                }

                .anim-item {
                    animation-fill-mode: both;
                    animation-duration: 300ms;
                }

                &.active {
                    opacity: 1;
                    visibility: visible;
                }

                &.active .anim-item {
                    opacity: 1;
                    visibility: visible;
                    animation-name: dropdown;
                }
            }

            .dropdown-container2 {
                // width: 167px;
                border-radius: 12px;
                background: #EEE;
                padding: 12px;
                box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05), 0px 8px 10px 1px rgba(0, 0, 0, 0.06), 0px 5px 5px -3px rgba(0, 0, 0, 0.10);
                position: absolute;
                top: 100%;
                opacity: 0;
                visibility: hidden;
                margin-top: 12px;

                .dropdown-card {
                    padding: 12px 0;
                    border-radius: 8px;
                    background-color: #fff;
                }

                .menu-items {
                    background: #fff;
                    // border-radius: 8px;
                    padding-top: 12px;
                    padding-bottom: 12px;
                }

                .menu-item {
                    display: flex;
                    // width: 145px;
                    padding: 12px 24px;
                    justify-content: center;
                    align-items: center;
                    color: rgba(0, 0, 0, 0.80);
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 21px;
                    background-color: #fff;
                    cursor: pointer;

                    &:hover {
                        background: #EEE;
                    }
                }

                .anim-item {
                    animation-fill-mode: both;
                    animation-duration: 300ms;
                }

                &.active {
                    opacity: 1;
                    visibility: visible;
                }

                &.active .anim-item {
                    opacity: 1;
                    visibility: visible;
                    animation-name: dropdown;
                }
            }
        }
    }

    .search-container {
        width: 0px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 24px 0 auto;
        cursor: pointer;
        // transition: width 0.3s ease-in-out;

        .search-icon {
            display: inline-flex;
            width: 18px;
            height: 18px;
            mask-image: url('../assets/imgs/search_icon.svg');
            mask-repeat: no-repeat;
            mask-size: cover;
            background-color: #252525;
            flex: 0 0 auto;
        }

        .search-input {
            display: none;
        }

        .search-cancel-icon {
            display: none;
        }

        .search-preview-container {
            display: none;
            width: 561px;
            height: 222px;
            flex-shrink: 0;
            border-radius: 8px;
            background: #EEE;
            box-shadow: 0px 8px 12px 0px rgba(0, 0, 0, 0.12);
            padding: 12px;
            margin-top: 12px;
            position: absolute;
            top: 100%;
            cursor: initial;

            .preview-card {
                width: 100%;
                height: 100%;
                border-radius: 8px;
                background: #FFF;
                padding: 16px;

                .card-title {
                    color: rgba(0, 0, 0, 0.43);
                    font-family: "PingFang SC";
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 18px;
                }

                .preview-items {
                    display: flex;
                    flex-direction: column;
                    width: 100%;

                    .item {
                        display: flex;
                        height: 32px;
                        padding: 10px 0px;
                        align-items: center;
                        flex-shrink: 0;
                        color: rgba(0, 0, 0, 0.75);
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 18px;
                        cursor: pointer;
                    }
                }
            }

            &.active {
                display: block;
            }
        }


        &.active {
            width: 561px;
            height: 40px;
            border-radius: 40px;
            background: #FFF;
            padding: 10px 0;
            margin: 0 0 0 auto;

            .search-icon {
                margin: 0 11px;
            }

            .search-input {
                display: block;
                width: 100%;
                height: 20px;
                border: none;
                outline: none;
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.80);
            }

            .search-cancel-icon {
                display: block;
                width: 12px;
                height: 12px;
                mask-image: url('../assets/imgs/x_icon.svg');
                mask-repeat: no-repeat;
                mask-size: cover;
                background-color: #252525;
                cursor: pointer;
                margin: 0 14px;
                flex: 0 0 auto;
            }
        }
    }

    .shop-link {
        display: inline-flex;
        width: 112px;
        height: 32px;
        padding: 10px 12px;
        align-items: center;
        flex-shrink: 0;
        border-radius: 26px;
        margin-left: 24px;
        margin-right: 24px;
        background: url('../assets/imgs/shop.svg') no-repeat left 12px center, #FF5000;
        background-size: 20px 20px;
        padding-left: 44px;
        color: #FFF;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
    }

}

@media (max-width: 768px) {
    .nav-bar {
        display: flex;
        width: 100vw;
        min-width: initial;
        height: 44px;
        align-items: center;
        border-radius: 0;
        border: none;
        background: url('../assets/imgs/logo_cn.png') no-repeat center center, #fff;
        background-size: 85px 26px;
        backdrop-filter: initial;
        position: sticky;
        top: 0;
        left: 0;
        transform: initial;
        padding: 0;


        .nav-bar-nav {
            display: none;
            animation-fill-mode: both;
            animation-duration: 300ms;

            &.active {
                display: flex;
                flex-direction: column;
                align-items: initial;
                position: absolute;
                top: 44px;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: #fff;
                opacity: 1;
                visibility: visible;
                animation-name: fadeInLeft;
            }

            .nav-item {
                display: inline-flex;
                flex-direction: column;
                height: 40px;
                padding: 0;
                justify-content: initial;
                margin: 0 16px 8px;
                flex-shrink: 0;
                border-bottom: 1px solid #D8D8D8;
                border-radius: 0;

                .nav-bar-link {
                    width: 100%;
                    height: 40px;
                    justify-content: space-between;
                    color: rgba(0, 0, 0, 0.8);
                    font-size: 14px;
                    font-weight: 400;

                    &.dropdown::after {
                        --un-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none"><path d="M16 20H24" stroke="white" stroke-opacity="0.91" stroke-linecap="round"/><path d="M20 16L20 24" stroke="white" stroke-opacity="0.91" stroke-linecap="round"/></svg>') no-repeat;
                        content: "";
                        background-color: rgba(0, 0, 0, 0.65);
                        width: 40px;
                        height: 40px;
                        mask: var(--un-icon) no-repeat;
                        mask-size: 100% 100%;
                    }

                    &::after {
                        --un-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="6" height="10" viewBox="0 0 6 10" fill="none"><path d="M1 9L5 5L1 1" stroke="white" stroke-opacity="0.91" stroke-linecap="round" stroke-linejoin="round"/></svg>') no-repeat;
                        content: "";
                        background-color: rgba(0, 0, 0, 0.65);
                        width: 40px;
                        height: 40px;
                        mask: var(--un-icon) no-repeat;
                        mask-size: 6px 10px;
                        mask-position: center;
                    }
                }

                &.active {
                    height: initial;
                    color: inherit;
                    font-family: "PingFang SC";
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 500;
                    border-radius: 12px;
                    background: #FFF;
                    border-bottom: none;

                    .nav-bar-link {
                        color: inherit;
                        font-weight: inherit;

                        &.dropdown::after {
                            --un-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="41" viewBox="0 0 40 41" fill="none"><path d="M16 20.5H24" stroke="black" stroke-opacity="0.65" stroke-linecap="round"/></svg>') no-repeat;
                            content: "";
                            background-color: rgba(0, 0, 0, 0.65);
                            width: 40px;
                            height: 40px;
                            mask: var(--un-icon) no-repeat;
                            mask-size: 100% 100%;
                            -webkit-mask: var(--un-icon) no-repeat;
                            -webkit-mask-size: 100% 100%;
                            transform: rotate(180deg);
                        }
                    }
                }

                .dropdown-container {
                    width: 100%;
                    height: 180px;
                    flex-shrink: 0;
                    position: initial;
                    transform: initial;
                    opacity: 0;
                    visibility: hidden;
                    margin-top: 0;

                    &.no-left-menu {
                        width: 100%;
                        height: initial;

                        .dropdown-card .dropdown-card-content .menu-right {
                            padding-top: 0;
                        }
                    }

                    .dropdown-card {
                        width: 100%;
                        height: 100%;
                        border-radius: 0;
                        background: #EEE;
                        box-shadow: initial;
                        padding: 0;

                        .dropdown-card-content {
                            display: flex;
                            flex-direction: row;
                            width: 100%;
                            height: 100%;
                            border-radius: 8px;
                            background: #FFF;

                            .menu-left {
                                display: none;
                            }

                            .menu-right {
                                flex-wrap: nowrap;
                                padding-top: 0;
                                overflow: auto;
                                margin-left: 0;

                                .menu-content-card {
                                    width: 146px;
                                    height: 166px;
                                    flex-shrink: 0;
                                    border-radius: 4px;
                                    background: #eee;
                                    margin-right: 8px;
                                    margin-bottom: 8px;

                                    .card-imgs {
                                        max-width: 100%;
                                        max-height: 122px;
                                        flex-shrink: 0;
                                        position: relative;

                                        .card-img {
                                            width: 100%;
                                            height: 100%;
                                            padding: 4px;
                                            object-fit: contain;
                                            transition: all 0.3s ease-in-out;
                                            position: absolute;
                                            top: 0;
                                            left: 0;
                                        }

                                        .img-1 {
                                            opacity: 1;
                                        }

                                        .img-2 {
                                            opacity: 0;
                                        }

                                        &:hover {
                                            .img-1 {
                                                opacity: 1;
                                            }

                                            .img-2 {
                                                opacity: 0;
                                            }
                                        }
                                    }

                                    .card-text {
                                        color: rgba(0, 0, 0, 0.80);
                                        text-align: center;
                                        font-family: "PingFang SC";
                                        font-size: 12px;
                                        font-style: normal;
                                        font-weight: 400;
                                        line-height: 18px;
                                        padding: 0 10px;
                                        margin: 0;
                                    }
                                }
                            }
                        }
                    }

                    .anim-item {
                        animation-fill-mode: both;
                        animation-duration: 300ms;
                    }

                    &.active {
                        opacity: 1;
                        visibility: visible;
                    }

                    &.active .anim-item {
                        opacity: 1;
                        visibility: visible;
                        animation-name: dropdown;
                    }
                }

                .dropdown-container2 {
                    // width: 167px;
                    width: 100%;
                    height: initial;
                    flex-shrink: 0;
                    box-shadow: initial;
                    position: initial;
                    transform: initial;
                    opacity: 0;
                    visibility: hidden;
                    margin-top: 0;
                    background: initial;
                    border-radius: 0;
                    padding: 0;

                    .menu-items {
                        background: #fff;
                        border-radius: 0;
                        padding-top: 0;
                        padding-bottom: 0;
                    }

                    .menu-item {
                        display: flex;
                        // width: 145px;
                        padding: 8px;
                        justify-content: initial;
                        align-items: center;
                        color: rgba(0, 0, 0, 0.80);
                        font-family: "PingFang SC";
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 18px;
                        cursor: pointer;

                        &:hover {
                            background: initial;
                        }
                    }

                    .anim-item {
                        animation-fill-mode: both;
                        animation-duration: 300ms;
                    }

                    &.active {
                        opacity: 1;
                        visibility: visible;
                    }

                    &.active .anim-item {
                        opacity: 1;
                        visibility: visible;
                        animation-name: dropdown;
                    }
                }
            }
        }

        .search-container {
            margin: 0 10px 0 auto;
            width: 32px;
            height: 32px;
            justify-content: center;
            // transition: width 0.3s ease-in-out;

            .search-icon {
                display: inline-flex;
                width: 16px;
                height: 16px;
                mask-image: url('../assets/imgs/search_icon.svg');
                mask-repeat: no-repeat;
                mask-size: cover;
                background-color: #252525;
                flex: 0 0 auto;
            }

            .search-input {
                display: none;
            }

            .search-cancel-icon {
                display: none;
            }

            .search-preview-container {
                display: none;
                width: 100%;
                height: 100vh;
                border-radius: 0;
                flex-shrink: 0;
                padding: 0;
                margin-top: 0;
                position: absolute;
                top: 100%;
                left: 0;
                animation-fill-mode: both;
                animation-duration: 300ms;
                -webkit-overflow-scrolling: touch;

                .preview-card {
                    border-radius: 0;
                    padding: 8px 0;
                    overflow: auto;

                    .card-title {
                        color: rgba(0, 0, 0, 0.65);
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 21px;
                        padding: 0 16px;
                    }

                    .preview-items {
                        display: flex;
                        flex-direction: column;
                        width: 100%;
                        padding: 0 16px;

                        .item {
                            display: block;
                            height: 32px;
                            padding: 0px;
                            align-items: center;
                            color: rgba(0, 0, 0, 0.8);
                            font-family: "PingFang SC";
                            font-weight: 500;
                            font-size: 14px;
                            line-height: 32px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }

                    .search-res-container {

                        .type-tabs {
                            display: flex;
                            align-items: center;
                            border-bottom: 1px solid #D8D8D8;
                            padding: 0 16px;
                            margin-bottom: 13px;

                            .tab-item {
                                display: flex;
                                height: 40px;
                                padding: 10px;
                                align-items: center;
                                flex-shrink: 0;
                                color: rgba(0, 0, 0, 0.65);
                                font-family: "PingFang SC";
                                font-size: 14px;
                                font-weight: 400;
                                line-height: 21px;
                                margin-right: 8px;

                                &.active {
                                    color: rgba(223, 4, 40, 1);
                                    border-bottom: 1px solid var(--Color-, #DF0428);
                                }
                            }
                        }

                        .search-res-content {
                            padding: 0 16px;

                            .res-item {
                                display: flex;
                                width: 100%;
                                height: 111px;
                                margin-bottom: 24px;
                                border-bottom: 1px solid #D8D8D8;

                                .content-left {
                                    width: 130px;
                                    height: 110px;
                                    padding: 10px;
                                    margin-right: 12px;
                                    flex: 0 0 auto;

                                    .content-img {
                                        width: 100%;
                                        height: 100%;
                                        object-fit: contain;
                                    }
                                }

                                .content-right {
                                    display: flex;
                                    flex-direction: column;
                                    width: 200px;
                                    flex: 1 1 auto;

                                    .content-name {
                                        height: 21px;
                                        color: rgba(0, 0, 0, 0.80);
                                        font-family: "PingFang SC";
                                        font-size: 14px;
                                        font-weight: var(--title-font-weight);
                                        line-height: 21px;
                                        margin-bottom: 8px;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                        white-space: nowrap;
                                    }

                                    .content-title {
                                        height: 42px;
                                        color: rgba(0, 0, 0, 0.65);
                                        font-family: "PingFang SC";
                                        font-size: 14px;
                                        font-weight: 400;
                                        line-height: 21px;
                                        //最多2行
                                        display: -webkit-box;
                                        -webkit-box-orient: vertical;
                                        -webkit-line-clamp: 2;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                    }

                                    .content-link {
                                        display: flex;
                                        align-items: center;
                                        height: 40px;
                                        color: #DF0428;
                                        font-family: "PingFang SC";
                                        font-size: 12px;
                                        font-style: normal;
                                        font-weight: 400;
                                        line-height: 18px;

                                        .link-text {
                                            margin-right: 8px;
                                        }

                                        .link-icon {
                                            --un-icon: url('../assets/imgs/icon_right_arrow.svg') no-repeat;
                                            background-color: #DF0428;
                                            display: inline-block;
                                            width: 16px;
                                            height: 16px;
                                            mask: var(--un-icon) no-repeat;
                                            mask-size: 100% 100%;
                                        }

                                        .news-text {
                                            display: inline-flex;
                                            padding: 2px 4px;
                                            justify-content: center;
                                            align-items: center;
                                            border-radius: 4px;
                                            background-color: #eee;
                                            color: rgba(9, 9, 9, 0.43);
                                            font-family: "PingFang SC";
                                            font-size: 10px;
                                            font-style: normal;
                                            font-weight: 400;
                                            line-height: 15px;
                                            margin-right: 8px;
                                        }

                                        .news-time {
                                            color: rgba(9, 9, 9, 0.43);
                                            font-family: "PingFang SC";
                                            font-size: 10px;
                                            font-style: normal;
                                            font-weight: 400;
                                            line-height: 15px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                &.active {
                    display: block;
                    animation-name: fadeInRight;
                }
            }


            &.active {
                // width: 305px;
                height: 32px;
                border-radius: 40px;
                background: #eee;
                padding: 0 0 0 12px;
                margin: 0 16px 0 72px;

                .search-icon {
                    margin: 0 18px 0 10px;
                }

                .search-input {
                    display: block;
                    width: 100%;
                    height: 100%;
                    border: none;
                    outline: none;
                    background-color: transparent;
                    font-family: "PingFang SC";
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 18px;
                    color: rgba(0, 0, 0, 0.65);
                }

                .search-cancel-icon {
                    display: block;
                    width: 40px;
                    height: 40px;
                    mask-image: url('../assets/imgs/icon_right_arrow.svg');
                    mask-repeat: no-repeat;
                    mask-size: 20px 20px;
                    mask-position: center center;
                    background-color: rgba(0, 0, 0, 0.8);
                    transform: rotate(180deg);
                    margin: 0;
                    position: absolute;
                    left: 16px;
                }
            }
        }

        .shop-link {
            --un-icon: url(/_nuxt/assets/imgs/shop.svg) no-repeat;
            background: rgba(0, 0, 0, 0.8);
            display: inline-block;
            width: 40px;
            height: 40px;
            mask: var(--un-icon) no-repeat;
            mask-size: 50% 50%;
            mask-position: center;
            padding: 0;
            margin-left: 0;
            margin-right: 16px;

            .shop-link-text {
                display: none;
            }

        }
    }
}

.icon-navbar-toggler {
    --un-icon: url('../assets/imgs/icon_nav_collapsed.svg') no-repeat;
    background-color: rgba(0, 0, 0, 0.8);
    display: inline-block;
    width: 40px;
    height: 40px;
    mask: var(--un-icon) no-repeat;
    mask-size: 100% 100%;
    transition: all 0.3s ease-in-out;

    &.active {
        --un-icon: url('../assets/imgs/icon_nav_expanded.svg') no-repeat;
        transform: rotate(180deg);
    }
}

.news-modal {
    // display: none;
    width: 100vw;
    height: 100vh;
    background: #fff;
    position: fixed;
    top: 0;
    left: 0;

    .news-iframe {
        width: 100%;
        height: 100%;
    }

    .top-btns {
        display: flex;
        align-items: center;
        width: 100%;
        height: 44px;
        background: #fff;
        background: url(/assets/imgs/logo_cn.png) no-repeat center center, #fff;
        background-size: 85px 26px;

        .icon-close {
            position: absolute;
            right: 16px;
        }
    }
}

.icon-close {
    display: block;
    width: 40px;
    height: 40px;
    background: url('../assets/imgs/icon_close.svg') no-repeat center center, rgba(0, 0, 0, 0.8);
    border-radius: 100%;
}
</style>