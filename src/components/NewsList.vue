<template>
    <div class="news-list">
        <div class="new-container">

            <div class="news-type">
                <div class="title">分类</div>
                <div class="type-items">
                    <div class="type-item" :class="{ active: item.value == activeType }"
                        v-for="(item, index) in NEWS_TYPE_OPTIONS" :key="index" @click="selType(item.value)">{{
                        item.label }}</div>
                </div>
            </div>
            <div class="news-items" v-if="data[activeType]">
                <div class="news-item" v-for="(item, index) in data[activeType].slice(0, sliceLength)" :key="index" @click="curNews = item">
                    <img :src="item.image" alt="" class="news-img">
                    <div class="new-content">
                        <div class="news-title">{{ item.title }}</div>
                        <div class="news-sub-title">{{ item.subTitle }}</div>
                        <div class="news-time">{{ item.time }}</div>
                    </div>
                </div>
                <div class="more-news-btn" @click="showAllnews()" v-if="sliceLength == 6">
                    <span>查看更多内容</span>
                    <i class="icon-right-arrow"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="news-content" v-if="curNews">
        <div class="close-btn">
            <i class="icon-x" @click="curNews = null"></i>
        </div>
        <div class="content-rich-text" v-html="curNews.content"></div>
    </div>
</template>
<script setup>
const config = useRuntimeConfig();
const baseURL = config.public.NUXT_API_BASE;

const data = ref({});

const NEWS_TYPE_OPTIONS = ref([])

const NEWS_TYPE_STR = ref({})

const activeType = ref(0);
const sliceLength = ref(6);
const curNews = ref(null);


const { data: newsTypeData } = await useAsyncData(
    () => $fetch(baseURL + '/news/news-type')
)
newsTypeData.value.data.map(item => {
    NEWS_TYPE_OPTIONS.value.push({
        label: item.label,
        value: item.value
    })
    NEWS_TYPE_STR.value[item.value] = item.label;
})
selType(NEWS_TYPE_OPTIONS.value[0].value);



const { data: newsData } = await useAsyncData(
    () => $fetch(baseURL + '/news/list?ps=10000')
)
parseData(newsData.value.data.list);
function parseData(list) {
    list.map(item => {
        let typeName = item.newsType;
        if (!data.value[typeName]) {
            data.value[typeName] = [];
            data.value[typeName].push(item);
        } else {
            data.value[typeName].push(item);
        }
    });
    console.log(data.value);
}


function selType(type) {
    activeType.value = type;
    hideAllnews();
}

function showAllnews() {
    sliceLength.value = 10000;
}
function hideAllnews() {
    sliceLength.value = 6;
}
</script>
<style lang="less" scoped>
.news-list {
    margin-bottom: var(--component-mb);

    .top-img-container {
        display: flex;
        width: 100%;
        aspect-ratio: 1920/500;
        margin-bottom: 100px;
        position: relative;

        .title {
            color: rgba(255, 255, 255, 0.97);
            font-family: "PingFang SC";
            font-size: 40px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: 2px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
            text-align: center;

            &.left {
                left: 240px;
                bottom: 100px;
                top: initial;
                transform: initial;
            }
        }

        .top-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
            position: relative;
        }
    }

    .new-container {
        display: flex;
        max-width: 1480px;
        // margin: 0 calc((100vw - 1440px) / 2);
        margin: 0 auto;
        padding: 0 20px;

        .news-type {
            display: flex;
            flex-direction: column;
            width: 220px;
            margin-right: 24px;
            flex: 0 0 auto;

            .title {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 18px;
                font-style: normal;
                font-weight: 400;
                line-height: 27px;
                margin-bottom: 24px;
            }

            .type-items {
                display: flex;
                flex-direction: column;
                width: 100%;
                gap: 12px;

                .type-item {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 40px;
                    color: rgba(0, 0, 0, 0.80);
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    letter-spacing: 0.7px;
                    cursor: pointer;

                    &.active {
                        background-color: #DF0428;
                        border-radius: 20px;
                        color: #fff;
                    }
                }
            }
        }

        .news-items {
            display: flex;
            flex-wrap: wrap;
            gap: 24px;
            position: relative;

            .news-item {
                display: flex;
                flex-direction: column;
                width: 381px;
                height: 347px;
                flex-shrink: 0;
                background-color: #fff;
                border-radius: 12px;
                cursor: pointer;

                .news-img {
                    width: 100%;
                    height: 218px;
                    border-radius: 12px 12px 0px 0px;
                    margin-bottom: 12px;
                    object-fit: cover;
                }

                .new-content {
                    .news-title {
                        color: rgba(0, 0, 0, 0.80);
                        font-family: "PingFang SC";
                        font-size: 18px;
                        font-style: normal;
                        font-weight: var(--title-font-weight);
                        line-height: 27px;
                        margin-bottom: 12px;
                        padding: 0 12px;
                    }

                    .news-sub-title {
                        color: rgba(0, 0, 0, 0.65);
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 21px;
                        margin-bottom: 24px;
                        padding: 0 12px;
                    }

                    .news-time {
                        color: rgba(9, 9, 9, 0.43);
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 21px;
                        padding: 0 12px;
                    }
                }


            }
        }

        .more-news-btn {
            display: flex;
            height: 56px;
            padding: 12px 24px;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            color: rgba(255, 255, 255, 0.97);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 15px;
            border-radius: 32px;
            background: linear-gradient(180deg, #000 -42.86%, #555 100%);
            margin: 16px auto 0;
            position: absolute;
            left: 50%;
            bottom: -40px;
            transform: translate(-50%, 100%);

            &:hover {
                .icon-right-arrow {
                    margin-left: 22px;
                }
            }

            .icon-right-arrow {
                background-color: rgba(255, 255, 255, 0.97);
                margin-left: 12px;
                transition: all 0.3s ease-in-out;
            }
        }
    }
}

.news-content {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 1000;
    .close-btn{
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: flex-end;
        padding: 20px;
        .icon-x{
            font-size: 24px;
            background-color: #000;
            cursor: pointer;
        }
    }
    .content-rich-text{
        height: 100vh;
        overflow: auto;
        padding: 24px;
        :deep(ul) {
            padding-inline-start: 40px;
        }
    }
}

@media (max-width: 768px) {
    .news-list {
        background-color: #fff;

        .top-img-container {
            height: initial;
            min-height: initial;
            aspect-ratio: 393/200;
            margin-bottom: 24px;
            cursor: pointer;

            .title {
                font-size: 16px;
                line-height: 24px;

                &.left {
                    left: 16px;
                    bottom: 24px;
                }
            }

            .top-img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                cursor: pointer;
                position: relative;
            }
        }

        .new-container {
            flex-direction: column;


            .title {
                display: none;
            }

            .news-type {
                width: 100%;
                margin: 0 16px 16px;

                .type-items {
                    width: initial;
                    flex-direction: row;
                    gap: 16px;

                    .type-item {
                        width: initial;
                        background-color: #eee;
                        border-radius: 6px;
                        font-size: 14px;
                        letter-spacing: 0.7px;
                        padding: 10px;

                        &.active {
                            border-radius: 6px;
                        }
                    }

                }
            }

            .news-items {
                gap: 0;
                padding: 0;

                .news-item {
                    width: 100%;
                    flex: 1 1 auto;
                    height: initial;
                    justify-content: space-between;
                    flex-direction: row-reverse;
                    border-bottom: 1px solid #D8D8D8;
                    border-radius: 0;
                    margin: 0 16px;
                    padding: 16px 0 11px;
                    gap: 16px;

                    .news-img {
                        width: 120px;
                        height: 90px;
                        flex: 0 0 auto;
                        border-radius: 6px;
                        margin-bottom: 0;
                    }

                    .new-content {
                        .news-title {
                            padding: 0;
                        }

                        .news-sub-title {
                            padding: 0;
                        }

                        .news-time {
                            padding: 0;
                        }
                    }
                }
            }

            .news-item:nth-last-child(1) {
                margin-bottom: 24px;
            }

            .more-news-btn {
                height: 28px;
                padding: 8px 12px;
                border-radius: 20px;
                font-size: 12px;
                line-height: 18px;
                position: initial;
                transform: initial;
                margin: 24px auto 32px;
            }
        }
    }
}
</style>