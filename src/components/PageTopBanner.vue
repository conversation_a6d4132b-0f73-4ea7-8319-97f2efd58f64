<template>
    <div ref="banner" class="page-top-banner">
        <div class="title" :style="{ color: data.textColor }" :class="{'left': data.titleLeft == 'yes'}" v-if="data.title">{{ data.title }}</div>
        <img :src="$patchRawUrl(data.resource.data.url.url)" alt="" class="banner-img" v-if="data.resource.type === 'image'">
        <div class="video-container banner-img" v-if="data.resource.type === 'video'" @click="togglePlay">
            <div class="play-btn" @click.stop="togglePlay" v-show="!isPlaying"></div>
            <video ref="video" :src="$patchRawUrl(data.resource.data.url.url)" class="video" muted loop>
            </video>
        </div>
    </div>
</template>
<script setup lang='ts'>

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
})

const isPlaying = ref(false);

const banner = ref<HTMLElement>();
const togglePlay = () => {
    const video = banner.value?.querySelector('video');
    if (video) {
        if (video.paused) {
            video.play();
            setTimeout(() => {
                isPlaying.value = true;
            }, 1000);
        } else {
            video.pause();
            isPlaying.value = false;
        }
    }
};
</script>
<style lang="less" scoped>
.page-top-banner {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    aspect-ratio: 1920/500;
    position: relative;
    margin-bottom: var(--component-mb);
    cursor: pointer;

    .title {
        color: rgba(255, 255, 255, 0.97);
        font-family: "PingFang SC";
        font-size: 40px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: 2px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        text-align: center;
        &.left{
            left: 240px;
            bottom: 100px;
            top: initial;
            transform: initial;
        }
    }

    .banner-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        cursor: pointer;
        position: relative;
    }

    .play-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
        z-index: 1;
    }

    .video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

@media (max-width: 768px) {
    .page-top-banner {
        height: initial;
        min-height: initial;
        // margin-bottom: 24px;
        cursor: pointer;

        .title {
            font-size: 16px;
            line-height: 24px;
            &.left{
                left: 16px;
                bottom: 24px;
            }
        }

        .banner-img {
            width: 100%;
            height: initial;
            aspect-ratio: 393/200;
            object-fit: cover;
            cursor: pointer;
            position: relative;
        }

        .play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 1;
        }

        .video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}
</style>