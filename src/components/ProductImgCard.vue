<template>
    <div class="product-img-card">
        <div class="title" :style="{ color: data.textColor }">{{data.title}}</div>
        <div class="sub-title" :style="{ color: data.textColor }">{{data.subTitle}}</div>
        <div class="item" v-for="(item, index) in data.list" :key="index">
            <div class="item-left">
                <div class="item-title">{{ item.title }}</div>
                <div class="item-sub-title">{{ item.subTitle }}</div>
            </div>
            <div class="item-right">
                <img :src="$patchRawUrl(item.resource.data.url.url)" alt="" class="item-resource" v-if="item.resource.type === 'image'">

                <div class="video-container item-resource" v-if="item.resource.type === 'video'"
                    @click="togglePlay($event, index)">
                    <div class="play-btn" v-show="!isPlaying[index]"></div>
                    <video ref="video" :src="$patchRawUrl(item.resource.data.url.url)" class="video" muted loop>
                    </video>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang='ts'>

const props = defineProps({
    data: {
        type: Object,
        default: () => { }
    }
});

const isPlaying = ref([]);
const togglePlay = (event, index) => {
    const container = event.currentTarget;
    const video = container.querySelector('video');
    if (video) {
        if (video.paused) {
            video.play();
            isPlaying.value[index] = true;
        } else {
            video.pause();
            isPlaying.value[index] = false;
        }
    }
};

</script>
<style lang="less" scoped>
.product-img-card {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0 0;
    margin-bottom: var(--component-mb);

    .title {
        max-width: 816px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--title-theme-color);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 48px;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        margin-bottom: 24px;
        margin: 0 auto 24px;
    }

    .sub-title {
        max-width: 1260px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--sub-title-theme-color);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 24px;
        font-weight: var(--sub-title-font-weight);
        line-height: 36px;
        margin-bottom: 39px;
        margin: 0 auto 39px;
    }

    .item {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 448px;
        max-width: 1440px;
        margin: 0 auto 40px;

        &:last-child {
            margin-bottom: 0;
        }

        &:nth-child(even) {
            flex-direction: row-reverse;

            .item-left {
                padding-right: 0;
                padding-left: 146px;
            }
        }

        .item-left {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 50%;
            padding-right: 146px;
            flex: 0 0 auto;
            margin-right: 12px;

            .item-title {
                color: var(--title-theme-color);
                font-family: "PingFang SC";
                font-size: 32px;
                font-weight: var(--title-font-weight);
                line-height: 48px;
                margin-bottom: 24px;
            }

            .item-sub-title {
                color: var(--sub-title-theme-color);
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: var(--sub-title-font-weight);
                line-height: 21px;
            }
        }

        .item-right {
            width: 50%;
            flex: 1;
            border-radius: 12px;
            overflow: hidden;

            .item-resource {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: relative;

                .play-btn {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    cursor: pointer;
                    z-index: 1;
                }

                .video {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .product-img-card {
        margin-top: 40px;
        // margin-bottom: 40px;
        padding: 0 16px;

        .title {
            font-size: 18px;
            line-height: 21px;
            margin-bottom: 4px;
        }

        .sub-title {
            font-size: 14px;
            line-height: 21px;
            margin-bottom: 12px;
        }

        .item {
            position: relative;
            margin-bottom: 12px;
            height: auto;
            aspect-ratio: 361/330;
        }

        .item .item-left {
            text-align: center;
            width: 100%;
            padding: 0 20px;
            margin: 0;
            position: absolute;
            top: 20px;
            z-index: 1;

            .item-title {
                color: var(--, rgba(255, 255, 255, 0.97));
                font-family: "PingFang SC";
                font-size: 16px;
                font-weight: var(--title-font-weight);
                line-height: 21px;
            }
        }

        .item:nth-child(even) .item-left {
            padding: 0;
        }

        .item .item-sub-title {
            display: none;
        }

        .item .item-right {
            border-radius: 6px;
        }
    }
}
</style>