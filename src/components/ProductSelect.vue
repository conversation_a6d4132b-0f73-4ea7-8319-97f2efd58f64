<template>
    <div class="product-select">
        <div class="header">
            <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
            <p class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</p>
        </div>
        <div class="product-list">
            <div class="item-container" v-for="(item, index) in data.productList" :key="index">
                <img :src="$patchRawUrl(item.img.url)" alt="" class="item-img">
                <div class="item-name">{{ item.title }}</div>
                <div class="item-sub-title">{{ item.subTitle }}</div>
                <a class="btn-more" :href="item.jumpLink">
                    <div class="keep-btn-left" v-if="data.type !== 'no-btn'">
                        <span class="btn-text">了解更多</span>
                        <i class="icon-right-arrow"></i>
                    </div>
                </a>
                <div class="line"></div>
                <div class="specs">
                    <div class="specs-item" v-for="(specs, specsIndex) in item.specs.slice(0, 7)" :key="specsIndex">
                        <span class="specs-name">{{ specs.key }}</span>
                        <span class="specs-value">{{ specs.value }}</span>
                    </div>
                    <div class="have-more">...</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: []
            }
        }
    }
})

const showModal = ref(false)
const currentVideoUrl = ref('');

const toVideoHome = () => {
    const url = props.data.videoHome || 'https://space.bilibili.com/484123810?spm_id_from=333.337.0.0';
    window.open(url, '_blank');
}

const openModal = (video: any) => {
    currentVideoUrl.value = video.url
    showModal.value = true
    document.body.style.overflow = 'hidden'
}

const closeModal = () => {
    showModal.value = false
    document.body.style.overflow = 'auto'
}
</script>

<style lang="less" scoped>
.product-select {
    width: 100%;
    background: #fff;
    padding: 0 20px 100px;
    margin-bottom: var(--component-mb);

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding-top: 100px;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            max-width: 1260px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .product-list {
        display: flex;
        justify-content: center;
        max-width: 1440px;
        margin: 64px auto 0;
        column-gap: calc(100% / 3 - 320px);

        .item-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 374px;
            // height: 800px;
            flex: 0 0 auto;

            &:first-child {
                margin-left: 0;
            }

            &:last-child {
                margin-right: 0;
            }

            .item-img {
                width: 305px;
                height: 212px;
                object-fit: contain;
                margin-bottom: 40px;
            }

            .item-name {
                color: var(--, rgba(0, 0, 0, 0.80));
                text-align: center;
                font-family: "PingFang SC";
                font-size: 18px;
                font-weight: 400;
                line-height: 27px;
                margin-bottom: 12px;
            }

            .item-sub-title {
                color: rgba(0, 0, 0, 0.65);
                text-align: center;
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 21px;
                margin-bottom: 24px;
            }

            .line {
                width: 100%;
                height: 1px;
                background: #D8D8D8;
                margin: 40px 0;
            }

            .specs {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 24px;
                align-self: stretch;
                text-align: center;

                .specs-item {
                    display: flex;
                    flex-direction: column;

                    .specs-name {
                        color: rgba(0, 0, 0, 0.80);
                        text-align: center;
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-weight: var(--title-font-weight);
                        line-height: 21px;
                        margin-bottom: 4px;
                    }

                    .specs-value {
                        color: rgba(0, 0, 0, 0.65);
                        text-align: center;
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 21px;
                    }


                }

                .have-more {
                    color: #000;
                    text-align: center;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-weight: var(--title-font-weight);
                    line-height: 21px;
                }
            }
        }
    }

    .btn-more {
        width: 130px;
        height: 40px;
        z-index: 1;

        .keep-btn-left {
            display: flex;
            width: 130px;
            height: 40px;
            padding: 8px 16px;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
            border-radius: 60px;
            border: 1px solid rgba(0, 0, 0, 0.8);
            cursor: pointer;
            position: relative;
            transition: all 0.1s ease-in-out;

            .btn-text {
                color: rgba(0, 0, 0, 0.8);
                font-size: 16px;
                font-style: normal;
                font-weight: 300;
                line-height: 24px;
            }

            .icon-right-arrow {
                background-color: rgba(0, 0, 0, 0.8);
            }
        }

        &:hover .keep-btn-left {
            width: 140px;
            background: rgba(255, 255, 255, 0.2);
        }

    }
}

@media (max-width: 768px) {
    .product-select {
        padding: 0 16px 32px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }


        .product-list {
            display: flex;
            justify-content: center;
            max-width: 1440px;
            margin: 24px auto 0;
            column-gap: 16px;

            .item-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 50%;
                flex: 0 0 auto;

                &:first-child {
                    margin-left: 0;
                }

                &:last-child {
                    margin-right: 0;
                }

                .item-img {
                    width: 146px;
                    height: 129px;
                    object-fit: contain;
                    margin-bottom: 24px;
                }

                .item-name {
                    font-size: 14px;
                    line-height: 21px;
                    margin-bottom: 8px;
                }

                .item-sub-title {
                    font-size: 12px;
                    line-height: 18px;
                    margin-bottom: 12px;
                }

                .line {
                    margin: 24px 0;
                }

                .specs {
                    gap: 24px;

                    .specs-item {
                        display: flex;
                        flex-direction: column;

                        .specs-name {
                            font-size: 12px;
                            line-height: 18px;
                            margin-bottom: 4px;
                        }

                        .specs-value {
                            font-size: 12px;
                            line-height: 18px;
                        }
                    }

                    .have-more {
                        color: #000;
                        text-align: center;
                        font-family: "PingFang SC";
                        font-size: 12px;
                        font-weight: var(--title-font-weight);
                        line-height: 18px;
                    }
                }
            }

            .item-container {
                display: none;

                &:nth-child(1),
                &:nth-child(2) {
                    display: flex;
                }
            }
        }

        .btn-more {
            width: 110px;
            height: 30px;
            z-index: 1;

            .keep-btn-left {
                width: 110px;
                height: 30px;
                padding: 8px 12px;
                border-radius: 30px;

                .btn-text {
                    font-size: 12px;
                    line-height: 18px;
                    font-weight: var(--sub-title-font-weight);
                }

                .icon-right-arrow {
                    background-color: rgba(0, 0, 0, 0.8);
                }
            }

            &:hover .keep-btn-left {
                width: 110px;
                background: rgba(255, 255, 255, 0.2);
            }

        }
    }
}
</style>