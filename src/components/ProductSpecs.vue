<template>
    <div class="product-specs">
        <div class="al-center">
            <div class="header-container">
                <div class="product-name">{{ data.list[tabIndex].productName }}</div>
                <div class="product-tabs">
                    <span v-for="(tab, tabIdx) in data.list" :key="tabIdx" :class="{ 'active-tab': tabIndex == tabIdx }"
                        @click="tabIndex = tabIdx">
                        {{ tab.productName }}
                    </span>
                </div>
            </div>
            <div class="specs-container" v-for="(spec, index) in data.list[tabIndex].specs" :key="index">
                <h3 class="spec-type">{{ spec.type }}</h3>
                <div class="spec-items">
                    <div class="spec-item" :class="{ 'img-item': item.type === 'image' }"
                        v-for="(item, itemIdx) in spec.items" :key="itemIdx">
                        <template v-if="item.type === 'image'">
                            <div class="spec-image-container">
                                <img :src="$patchRawUrl(item.image.url)" alt="参数图片" class="spec-image" />
                            </div>
                        </template>
                        <template v-else>
                            <div class="key">{{ item.key }}</div>
                            <div class="value">{{ item.value }}</div>
                        </template>
                    </div>
                </div>
            </div>
            <div class="bottom-container">
                <div class="specs-download" @click="handleDownload(data.list[tabIndex].productDoc)">下载完整版参数PDF <i
                        class="icon_download"></i>
                </div>
            </div>

        </div>
        <!-- 用户信息表单弹窗 -->
        <SubmitInfoForm v-if="showSubmitForm" :data="submitFormData" @submit="handleFormSubmit"
            @close="closeSubmitForm" />
    </div>
</template>

<script setup>
import { ref } from 'vue';
const config = useRuntimeConfig();
const baseURL = config.public.NUXT_API_BASE;

const tabIndex = ref(0);
const showSubmitForm = ref(false);
const currentDownloadFile = ref(null);
const userHasSubmitted = ref(false);

const props = defineProps({
    data: {
        type: Object,
        default: () => { }
    }
});

const res = await useAsyncData(
    () => $fetch(baseURL + '/components/component-prop-data-by-name?name=ContactUs')
)

const contactData = res.data.value.data;

const submitFormData = ref({
    title: '即将开始下载',
    type: 'modal',
    privacyFile: contactData.privacyFile,
    usePolicy: contactData.usePolicy,
    productList: contactData.productList,
    downloadFile: null
})

// 直接下载文件的函数
const directDownload = (file) => {
    const a = document.createElement('a');
    a.href = file.url;
    a.download = file.name || 'download';
    document.body.appendChild(a);
    a.rel = 'noopener noreferrer';
    a.click();
    document.body.removeChild(a);
};

// 处理下载点击
const handleDownload = (file) => {
    currentDownloadFile.value = file;

    // 每次点击时都检查用户是否已经提交过信息
    const hasSubmitted = localStorage.getItem('user_has_submitted_download_form');
    userHasSubmitted.value = hasSubmitted === 'true';

    // 如果用户已经提交过信息，直接下载
    if (userHasSubmitted.value) {
        console.log('用户已提交过信息，直接下载文件', file.name);
        directDownload(file);
        return;
    }

    // 否则显示表单，并传递要下载的文件信息
    console.log('用户首次下载，显示表单');
    localStorage.setItem('tmp_download_file', JSON.stringify(file));
    submitFormData.value.downloadFile = file;
    showSubmitForm.value = true;
};

// 处理表单提交
const handleFormSubmit = (formData) => {
    console.log('用户提交的信息:', formData);

    // 记录用户已经提交过信息
    userHasSubmitted.value = true;
    localStorage.setItem('user_has_submitted_download_form', 'true');

    // 表单提交成功后，关闭表单
    showSubmitForm.value = false;
};

// 关闭表单
const closeSubmitForm = () => {
    showSubmitForm.value = false;
    currentDownloadFile.value = null;
    // 清理临时存储的文件信息
    localStorage.removeItem('tmp_download_file');
};
</script>

<style lang="less" scoped>
.product-specs {
    display: flex;
    justify-content: center;
    width: 100%;
    margin: 0 auto;
    padding: 80px 40px;
    background-color: var(--component-theme-bg-color);

    .al-center {
        width: 1440px;
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40px;
    }

    .product-name {
        font-family: "PingFang SC";
        font-weight: var(--title-font-weight);
        color: var(--component-theme-color);
        font-size: 32px;
        line-height: 48px;
        margin-bottom: 0;
    }

    .product-tabs {
        display: flex;
    }

    .product-tabs span {
        display: inline-block;
        width: 300px;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 16px;
        color: var(--component-theme-color);
        line-height: 22px;
        cursor: pointer;
        padding-bottom: 12px;
        border-bottom: 1px solid #D8D8D8;
        font-weight: var(--title-font-weight);
    }

    .product-tabs span.active-tab {
        border-bottom-color: #DF0428;
    }

    .specs-container {
        padding-bottom: 24px;
        margin-bottom: 40px;
        border-bottom: 1px solid var(--component-theme-border-color, #D8D8D8);

        .spec-type {
            font-family: "PingFang SC";
            color: rgba(0, 0, 0, 0.80);
            font-size: 18px;
            font-weight: var(--title-font-weight);
            line-height: 27px;
            margin-bottom: 24px;
        }

        .spec-items {
            display: flex;
            gap: 16px;

            .spec-item {
                flex: 0 0 calc((100% - 5*16px)/6);

                &:nth-last-child(1) {
                    text-align: right;
                }

                /* 6列，间隔16px */

                .spec-image-container {
                    width: 300px;
                    height: 162px;
                    object-fit: contain;
                    border-radius: 8px;
                    margin: 0 44px 40px;
                    position: relative;

                    .spec-image {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                        position: absolute;
                        top: 0;
                        left: 0;

                        &:hover {
                            transform: scale(1.2);
                            transform-origin: center;
                        }
                    }
                }

                .key {
                    font-family: "PingFang SC";
                    font-size: 14px;
                    color: var(--component-theme-sub-color);
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 21px;
                    margin-bottom: 12px;
                }

                .value {
                    font-family: "PingFang SC";
                    font-size: 14px;
                    color: var(--component-theme-color);
                    font-weight: var(--title-font-weight);
                    line-height: 21px;
                }
            }
        }
    }

    .bottom-container {
        display: flex;
        width: 100%;
        justify-content: center;
    }

    .specs-download {
        display: inline-flex;
        height: 56px;
        padding: 8px 24px;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        border-radius: 50px;
        background: #DF0428;
        color: #FFF;
        font-family: "PingFang SC";
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        margin: 40px 0;
        cursor: pointer;
    }

    @media (max-width: 768px) {
        .spec-items {
            gap: 12px;

            .spec-item {
                flex: 0 0 calc((100% - 2*12px)/3);
                /* 小屏3列 */
            }
        }
    }
}

@media (max-width: 768px) {
    .product-specs {
        display: flex;
        justify-content: flex-start;
        width: 100%;
        margin: 0;
        padding: 24px 16px;

        .header-container {
            margin-bottom: 20px;

            .product-tabs {
                border-bottom: 1px solid var(--component-theme-border-color, #D8D8D8);
            }

            .product-name {
                font-size: 16px;
                line-height: 21px;
                color: #DF0428;
            }

            .product-tabs span {
                width: initial;
                font-size: 14px;
                line-height: 21px;
                padding-bottom: 4px;
                padding-right: 10px;
                margin-right: 14px;
                border-bottom: none;
                position: relative;

                &.active-tab::after {
                    content: '';
                    display: inline-block;
                    width: 100%;
                    height: 1px;
                    background-color: #DF0428;
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                }

                &:nth-child(2) {
                    padding-right: 0;
                    padding-left: 10px;
                    margin-right: 0;
                }
            }
        }

        .specs-container {
            border-bottom: none;
            padding-bottom: 0;
            margin-bottom: 24px;

            .spec-type {
                font-size: 14px;
                line-height: 21px;
                margin-bottom: 12px
            }

            .spec-items {
                flex-direction: column;
                gap: 0;

                .spec-item {
                    flex: initial;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 40px;
                    border-top: 1px solid var(--component-theme-border-color, #D8D8D8);

                    &:nth-last-child(1) {
                        border-bottom: 1px solid var(--component-theme-border-color, #D8D8D8);
                    }

                    &.img-item {
                        height: auto;
                        justify-content: center;
                    }

                    .spec-image-container {
                        width: 246px;
                        height: 143px;
                        margin: 0;
                        // aspect-ratio: 246/143;
                    }

                    .key {
                        font-size: 12px;
                        line-height: 18px;
                        margin-bottom: 0;
                    }

                    .value {
                        font-size: 12px;
                        line-height: 18px;
                    }
                }
            }
        }

        .specs-download {
            height: 40px;
            padding: 4px 24px;
            margin: 0;
            font-size: 12px;
            line-height: 18px;

            .icon_download {
                width: 12px;
                height: 12px;
                mask-size: 12px;
            }
        }
    }
}

.icon_download {
    display: inline-block;
    width: 20px;
    height: 20px;
    mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none"> <path d="M10.5 7.5V9.5C10.5 9.76522 10.3946 10.0196 10.2071 10.2071C10.0196 10.3946 9.76522 10.5 9.5 10.5H2.5C2.23478 10.5 1.98043 10.3946 1.79289 10.2071C1.60536 10.0196 1.5 9.76522 1.5 9.5V7.5" stroke="white" stroke-linecap="round" stroke-linejoin="round"/> <path d="M3.5 5L6 7.5L8.5 5" stroke="white" stroke-linecap="round" stroke-linejoin="round"/> <path d="M6 7.5V1.5" stroke="white" stroke-linecap="round" stroke-linejoin="round"/> </svg>') no-repeat center center;
    mask-size: 100%;
    background-color: #fff;
    margin-left: 10px;
}
</style>