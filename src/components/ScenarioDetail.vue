<template>
    <div class="scenario-detail">

        <i class="icon-close sticky mt-24 ml-auto mr-24" @click="hideModal"></i>
        <!-- 区块1：Banner -->
        <section class="block banner-block">
            <banner2-in-scen :data="data.bannerData" />
        </section>

        <!-- 区块2：三图水平布局 -->
        <section class="block three-images-block">
            <h2 class="div">{{ data.painPointsData.title }}</h2>
            <div class="three-image-grid">
                <div v-for="(item, index) in data.painPointsData.items" :key="index" class="grid-item">
                    <img :src="$patchRawUrl(item.img.url)" alt="" class="grid-image">
                    <div class="content-box">
                        <h3 class="title">{{ item.title }}</h3>
                        <p class="subtitle">{{ item.subTitle }}</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 区块3：2+3网格布局 -->
        <section class="block five-images-block">
            <h2 class="div">{{ data.advantagesData.title }}</h2>
            <div class="dynamic-image-grid"
                :class="{ 'five-items': data.advantagesData.items.length === 5, 'four-items': data.advantagesData.items.length === 4 }">
                <div v-for="(item, index) in data.advantagesData.items" :key="index" class="grid-item">
                    <img :src="$patchRawUrl(item.img.url)" alt="" class="grid-image">
                    <div class="content-box">
                        <h3 class="title">{{ item.title }}</h3>
                    </div>
                </div>
            </div>
        </section>

        <section class="block">
            <banner2-in-scen :data="data.solutionBanner" />
            <div class="ten-images-container">
                <div class="left-container">
                    <div class="img-container">
                        <img :src="$patchRawUrl(data.productDetailData.mainProduct.img.url)" alt="" class="big-img">
                    </div>
                    <div>
                        <div class="left-container-title">{{data.productDetailData.mainProduct.title}}</div>
                        <div class="left-container-desc">{{data.productDetailData.mainProduct.desc}}</div>
                    </div>
                </div>
                <div class="right-grid">
                    <div class="grid-item" v-for="(item, index) in data.productDetailData.relatedProducts" :key="index">
                        <div class="img-desc">{{item.desc}}</div>
                        <img :src="$patchRawUrl(item.img.url)" alt="" class="item-img">
                    </div>
                </div>
            </div>
        </section>

        <section class="block" style="padding: 0;">
            <h2 class="div m-b-0">{{ data.excellentCasesData.title }}</h2>
            <h3 class="section-subtitle">{{ data.excellentCasesData.subTitle }}</h3>
            <div class="more-img">
                <div class="img-container" v-for="(item, index) in data.excellentCasesData.list" :key="index">
                    <img :src="$patchRawUrl(item.img.url)" alt="" class="item-img">
                    <div class="img-title">{{ item.title }}</div>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup lang="ts">
const props = defineProps({
    data: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['hideModal']);
const hideModal = () => {
    emit('hideModal');
}

</script>

<style lang="less" scoped>
.scenario-detail {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 12px;
    overflow: auto;
    padding: 0 0 80px;
    position: relative;

    .block {
        width: calc(100% - 160px);
        max-width: 1560px;
        min-width: 1080px;
        margin: 0 auto 200px;

        .div {
            color: rgba(0, 0, 0, 0.80);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-weight: var(--title-font-weight);
            line-height: 72px;
            text-align: center;
            margin-bottom: 40px;

            &.m-b-0 {
                margin-bottom: 0;
            }
        }

        .section-subtitle {
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-bottom: 40px;
        }

        &:nth-last-child(1) {
            margin-bottom: 0;
        }
    }

    .three-image-grid {
        display: flex;
        gap: 24px;

        .grid-item {
            aspect-ratio: 504/600;
            width: 504px;
            flex: 1;
            position: relative;
            border-radius: 12px;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.00) 100%);

            .grid-image {
                width: 100%;
                height: 100%;
                border-radius: 12px;
                object-fit: cover;
                position: relative;
            }

            .content-box {
                position: absolute;
                top: 40px;
                left: 0;
                right: 0;
                text-align: center;

                .title {
                    color: rgba(255, 255, 255, 0.91);
                    font-family: "PingFang SC";
                    font-size: 32px;
                    font-weight: var(--title-font-weight);
                    line-height: 48px;
                    margin-bottom: 24px;
                }

                .subtitle {
                    color: rgba(255, 255, 255, 0.91);
                    text-align: center;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 21px;
                    padding: 0 24px;
                    /* 150% */
                }
            }
        }
    }

    .dynamic-image-grid {
        aspect-ratio: 1560/864;
        display: grid;
        gap: 24px;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);

        &.five-items {
            grid-template-columns: repeat(6, 1fr);

            .grid-item:nth-child(1),
            .grid-item:nth-child(2) {
                grid-column: span 3;
            }

            .grid-item:nth-child(n+3) {
                grid-column: span 2;
            }
        }

        &.four-items {
            grid-template-columns: repeat(2, 1fr);

            .grid-item {
                grid-row: span 1;
            }
        }

        .grid-item {
            // height: 420px;
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.00) 100%);

            .grid-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .content-box {
                position: absolute;
                top: 40px;
                left: 40px;

                .title {
                    color: rgba(255, 255, 255, 0.91);
                    font-family: "PingFang SC";
                    font-size: 32px;
                    font-weight: var(--title-font-weight);
                    line-height: 48px;
                }
            }
        }
    }

    .ten-images-container {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .left-container {
            aspect-ratio: 637/870;
            max-width: 637px;
            margin-right: 24px;
            flex: 1;

            .img-container {
                width: 100%;
                aspect-ratio: 637/541;
                // height: 541px;
                padding: 40px;
                border-radius: 12px;
                background: #F3F3F3;
                margin-bottom: 26px;

                .big-img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    border-radius: 12px;
                    background: #F3F3F3;
                }
            }

            .left-container-title {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 32px;
                font-weight: var(--title-font-weight);
                line-height: 48px;
                margin-bottom: 23px;
                padding-right: 32px;
            }

            .left-container-desc {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 24px;
                font-style: normal;
                font-weight: 400;
                line-height: 36px;
                padding-right: 32px;
            }
        }

        .right-grid {
            aspect-ratio: 801/870;
            max-width: 801px;
            flex: 1;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 24px;

            .grid-item {
                // aspect-ratio: 251/274;
                // width: 251px;
                // height: 274px;
                border-radius: 12px;
                background: #F3F3F3;

                .img-desc {
                    width: 100%;
                    color: #000;
                    font-family: "PingFang SC";
                    font-size: 18px;
                    font-weight: var(--title-font-weight);
                    line-height: 27px;
                    padding: 24px 24px 0;
                    margin-bottom: 16px;
                    text-align: center;
                    //超出2行省略
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                    text-overflow: ellipsis;

                }

                .item-img {
                    aspect-ratio: 251/180;
                    display: flex;
                    width: 100%;
                    // height: 180px;
                    padding: 24px 32px;
                    justify-content: center;
                    align-items: center;
                    flex-shrink: 0;
                    object-fit: contain;
                }
            }
        }
    }


    .more-img {
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap: 24px;
        margin-top: 40px;

        .img-container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 504px;
            min-height: 225px;
            aspect-ratio: 504/330;
            position: relative;
            z-index: 0;

            .item-img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 12px;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.00) 100%);
                position: absolute;
                z-index: -1;
            }

            .img-title {
                color: #FFF;
                font-family: "PingFang SC";
                font-size: 32px;
                font-weight: var(--title-font-weight);
                line-height: 48px;
                padding: 24px;
            }
        }
    }

    .icon-close {
        display: inline-block;
        flex-shrink: 0;
        width: 56px;
        height: 56px;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none"><circle cx="28" cy="28" r="28" fill="black" fill-opacity="0.2"/><path d="M35.1406 21L20.9985 35.1421" stroke="white" stroke-width="3" stroke-linecap="round"/><path d="M35.1406 35.1426L20.9985 21.0004" stroke="white" stroke-width="3" stroke-linecap="round"/></svg>');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
        cursor: pointer;
    }

    .icon-close:hover {
        border-radius: 56px;
        background-color: rgba(0, 0, 0, 0.80);
    }

    .icon-close.sticky {
        position: sticky;
        top: 24px;
        z-index: 999
    }

    .icon-close.mt-24 {
        margin-top: 24px;
    }

    .icon-close.ml-auto {
        margin-left: auto;
    }

    .icon-close.mr-24 {
        margin-right: 24px;
    }
}

@media (max-width: 767px) {
    .scenario-detail {
        padding-bottom: 20px;

        .block {
            width: 100%;
            min-width: initial;
            max-width: initial;
            margin-bottom: 32px;
            padding: 0 16px;

            &.banner-block {
                // width: calc(100% - 32px);
            }

            .div {
                font-size: 18px;
                line-height: 27px;
                margin-bottom: 12px;
            }

            .section-subtitle {
                font-size: 14px;
                line-height: 21px;
                margin-top: 4px;
                margin-bottom: 12px;
            }
        }

        .three-image-grid {
            display: flex;
            gap: 16px;
            aspect-ratio: 393/180;
            // padding: 0 16px;
            overflow: auto;

            .grid-item {
                aspect-ratio: 147/180;
                height: 100%;
                border-radius: 6px;

                .grid-image {
                    border-radius: 6px;
                }

                .content-box {
                    top: 34px;

                    .title {
                        font-size: 14px;
                        line-height: 21px;
                        margin-bottom: 8px;
                        padding: 0 8px;
                    }

                    .subtitle {
                        font-size: 14px;
                        line-height: 21px;
                        padding: 0 8px;
                    }
                }
            }
        }

        .dynamic-image-grid {
            aspect-ratio: initial;
            display: grid;
            gap: 16px;
            // padding: 0 16px;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(1, 1fr);

            .grid-item {
                aspect-ratio: 172/190;
            }

            &.five-items {
                grid-template-columns: repeat(1, 1fr);

                .grid-item:nth-child(1),
                .grid-item:nth-child(2) {
                    grid-column: span 1;
                }

                .grid-item:nth-child(n+3) {
                    grid-column: span 1;
                }
            }

            &.four-items {
                grid-template-columns: repeat(1, 1fr);

                .grid-item {
                    grid-row: span 1;
                }
            }

            .grid-item {
                // height: 420px;
                position: relative;
                border-radius: 6px;
                overflow: hidden;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.00) 100%);

                .grid-image {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                .content-box {
                    position: absolute;
                    top: 40px;
                    left: 40px;

                    .title {
                        color: rgba(255, 255, 255, 0.91);
                        font-family: "PingFang SC";
                        font-size: 32px;
                        font-weight: var(--title-font-weight);
                        line-height: 48px;
                    }
                }
            }
        }

        .ten-images-container {
            display: flex;
            flex-direction: column;
            align-items: initial;
            justify-content: initial;
            margin-top: 12px;

            .left-container {
                display: flex;
                aspect-ratio: 361/179;
                flex: 1;
                overflow: hidden;
                margin-right: 0;
                margin-bottom: 12px;

                .img-container {
                    height: 100%;
                    aspect-ratio: 181/179;
                    // height: 541px;
                    padding: 12px;
                    border-radius: 6px;
                    background: #F3F3F3;
                    margin-bottom: 0;
                    margin-right: 23px;

                    .big-img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        border-radius: 6px;
                        background: #F3F3F3;
                    }
                }

                .left-container-title {
                    font-size: 16px;
                    line-height: 24px;
                    margin-bottom: 12px;
                    padding-right: 12px;
                }

                .left-container-desc {
                    font-size: 12px;
                    line-height: 18px;
                    padding-right: 9px;
                }
            }

            .right-grid {
                aspect-ratio: initial;
                max-width: initial;
                flex: 1;
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-template-rows: initial;
                row-gap: 12px;
                column-gap: 16px;

                .grid-item {
                    display: flex;
                    flex-direction: column;
                    aspect-ratio: 110/120;
                    border-radius: 6px;
                    background: #F3F3F3;

                    .img-desc {
                        width: 100%;
                        min-height: 42px;
                        color: #000;
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-weight: var(--title-font-weight);
                        line-height: 21px;
                        padding: 0 7px 0;
                        margin-top: 4px;
                        margin-bottom: 2px;
                        //超出2行省略
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        text-overflow: ellipsis;

                    }

                    .item-img {
                        aspect-ratio: 96/61;
                        display: flex;
                        width: 100%;
                        padding: 6px;
                        justify-content: center;
                        align-items: center;
                        flex-shrink: 0;
                        object-fit: contain;
                        margin-top: auto;
                    }
                }
            }
        }

        .more-img {
            display: flex;
            flex-direction: column;
            justify-content: initial;
            width: 100%;
            gap: 0;
            margin-top: 0;

            .img-container {
                width: 100%;
                min-height: auto;
                aspect-ratio: 393/200;
                margin-bottom: 12px;

                .item-img {
                    border-radius: 0;
                }

                .img-title {
                    font-size: 16px;
                    line-height: 24px;
                    padding: 12px;
                }
            }
        }
    }
}
</style>