<template>
    <div class="service-support">
        <div class="header">
            <div class="title" :style="{ color: data.textColor }">{{ data.title }}</div>
            <div class="sub-title" :style="{ color: data.textColor }">{{ data.subTitle }}</div>
        </div>
        <div class="cards-container">
            <div class="card" v-for="item in data.list" :key="item.title">
                <div class="card-icon">
                    <img :src="$patchRawUrl(item.icon.url)" />
                </div>
                <h3 class="card-title">{{ item.title }}</h3>
                <p class="card-desc">{{ item.desc }}</p>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {

            }
        }
    }
})
</script>

<style scoped lang="less">
.service-support {
    width: 100%;
    padding: 0 20px;
    margin-bottom: var(--component-mb);

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding-top: 0px;

        .title {
            max-width: 816px;
            text-align: center;
            color: rgba(0, 0, 0, 0.8);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: var(--title-font-weight);
            line-height: 72px;
        }

        .sub-title {
            max-width: 1260px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: var(--sub-title-font-weight);
            line-height: 36px;
            margin-top: 24px;
            margin-bottom: 40px;
        }
    }

    .cards-container {
        display: flex;
        justify-content: center;
        gap: 24px;

        .card {
            width: 342px;
            min-height: 266px;
            background: #FFFFFF;
            border-radius: 12px;
            padding: 40px 24px 24px;
            text-align: center;

            .card-icon {
                width: 64px;
                height: 64px;
                margin: 0 auto 24px;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .card-title {
                font-family: PingFang SC;
                font-size: 18px;
                font-weight: var(--title-font-weight);
                color: rgba(0, 0, 0, 0.8);
                line-height: 1.5;
                margin-bottom: 24px;
            }

            .card-desc {
                font-family: PingFang SC;
                font-size: 14px;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.65);
                line-height: 1.5;
            }
        }
    }
}

@media (max-width: 768px) {
    .service-support {
        padding: 0 16px;

        .header {
            padding-top: 32px;

            .title {
                font-size: 18px;
                line-height: 21px;
            }

            .sub-title {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                margin-bottom: 12px;
            }
        }

        .cards-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 16px;

            .card {
                width: 100%;
                min-height: 149px;
                background: #FFFFFF;
                border-radius: 12px;
                padding: 40px 24px 24px;
                text-align: center;

                .card-icon {
                    width: 64px;
                    height: 64px;
                    margin: 0 auto 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .card-title {
                    font-family: PingFang SC;
                    font-size: 18px;
                    font-weight: var(--title-font-weight);
                    color: rgba(0, 0, 0, 0.8);
                    line-height: 1.5;
                    margin-bottom: 24px;
                }

                .card-desc {
                    font-family: PingFang SC;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(0, 0, 0, 0.65);
                    line-height: 1.5;
                }
            }
        }
    }
}
</style>