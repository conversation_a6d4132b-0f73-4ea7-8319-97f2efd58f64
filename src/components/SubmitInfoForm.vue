<template>
    <div class="" :class="{ 'modal-mask': data.type == 'modal' }" @click="handleModalBackgroundClick">
        <div class="submit-form" @click.stop="hiadeProvince(); hideMultiSelect();">
            <div class="form-header">
                <h3 class="title" :style="{ color: data.textColor }">{{ data.title }}</h3>
                <i class="close-btn" @click="closeModal" v-if="data.type == 'modal'"></i>
            </div>

            <div class="form-grid">
                <div class="form-item" :class="{ 'textarea-item': item.type == 'textarea' }"
                    v-for="(item, index) in inputFields" :key="index">
                    <div class="input-container" @click="handleFocus(item.key)" v-if="item.type === 'input'"
                        :class="{ 'focused': activeField === item.key, 'error': errors[item.key], active: activeField === item.key || formData[item.key] }">
                        <label class="label">
                            <span class="required">*</span>
                            {{ item.label }}
                        </label>
                        <input class="input" type="text"
                            v-show="activeField == item.key || formData[item.key] || isMobile"
                            :placeholder="item.placeholder" v-model="formData[item.key]" @focus="handleFocus(item.key)"
                            @blur="handleBlur(item.key)" @input="handleInput(item.key)"
                            :ref="el => inputRefs[item.key] = el">
                    </div>
                    <div class="input-container select-container multi-select-container"
                        v-else-if="item.type === 'multi-select'"
                        @click.stop="toggleMultiSelect($event, item.key); handleFocus(item.key)"
                        :class="{ 'focused': activeField === item.key, 'error': errors[item.key], active: activeField === item.key || formData[item.key] }">
                        <label class="label">
                            <span class="required">*</span>
                            {{ item.label }}
                        </label>
                        <div class="selected">
                            {{ formData[item.key] }}
                        </div>
                        <div class="multi-dropdown" v-if="activeMultiSelect === item.key" @click.stop>
                            <div class="tab-bar">
                                <div class="tab-item" v-for="(tab, index) in item.tabs" :key="index"
                                    @click="activeMultiSelectTab = index"
                                    :class="{ 'active': activeMultiSelectTab === index }">
                                    {{ tab.name }}
                                </div>
                            </div>
                            <div class="tab-content">
                                <div v-for="(subType, subIndex) in item.tabs[activeMultiSelectTab].subTypes"
                                    :key="subIndex" class="sub-type">
                                    <div class="sub-type-title">{{ subType.name }}</div>
                                    <div class="option" v-for="(option, i) in subType.options" :key="i">
                                        <div class="custom-checkbox-container">
                                            <input class="custom-checkbox" type="checkbox" v-model="option.checked"
                                                @change="updateSelectedProducts(item)">
                                            <span class="checkmark"></span>
                                        </div>
                                        <span>{{ option.name }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="input-container select-container" v-else-if="item.type === 'select'"
                        @click="toggleDropdown($event); handleFocus(item.key)"
                        :class="{ 'focused': activeField === item.key, 'error': errors[item.key], active: activeField === item.key || formData[item.key] }">
                        <label class="label">
                            <span class="required">*</span>
                            {{ item.label }}
                        </label>
                        <div class="selected">
                            {{ selectedProvince }}
                        </div>
                        <div class="dropdown">
                            <ul v-if="isOpen" class="options">
                                <li v-for="province in item.options" :key="province"
                                    @click.stop="selectProvince(province, item.key)"
                                    :class="{ 'selected-item': province === selectedProvince }">
                                    {{ province }}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-textarea" v-else-if="item.type === 'textarea'">
                        <div class="input-container" @click="handleFocus('remark')"
                            :class="{ 'focused': activeField === 'remark', active: activeField === 'remark' || formData.remark }">
                            <!-- <label class="label">详细说明</label> -->
                            <div class="textarea-container">
                                <textarea v-model="formData.remark" @focus="handleFocus('remark')"
                                    :placeholder="item.placeholder" :maxlength="300" :rows="3" :cols="50"
                                    class="textarea" @blur="handleBlur('remark')"
                                    @input="handleTextareaInput"></textarea>
                                <span class="word-count">{{ textLength }}/300</span>
                            </div>
                        </div>
                    </div>
                    <div v-if="errors[item.key]" class="error-message">{{ errors[item.key] }}</div>
                </div>
            </div>



            <div class="form-footer">
                <div class="agree-check">
                    <div class="custom-checkbox-container">
                        <input class="custom-checkbox" type="checkbox" v-model="formData.agreed">
                        <span></span>
                    </div>
                    <div class="agree-text">
                        <span>已阅读并接受</span>
                        <a class="agree-doc" :href="patchRawUrl(data.privacyFile)" target="_blank">《隐私权政策》</a>
                        <span>及</span>
                        <a class="agree-doc" :href="patchRawUrl(data.usePolicy)" target="_blank">《使用条款》</a>
                    </div>
                    <div v-if="errors.agreed && !formData.agreed" class="agreed-error error-message">{{ errors.agreed }}
                    </div>
                </div>
                <div v-if="errors.agreed && !formData.agreed" class="hidden-mob" style="height: 18px;"> </div>
                <button class="submit-btn" :disabled="!formValid" @click="handleSubmit">
                    提交申请
                </button>
            </div>
        </div>
        <SuccessMsgModal :show="showsuccessMsgModal" @close="showsuccessMsgModal = false" />


    </div>
</template>

<script setup lang="ts">
import { fi } from '@formkit/i18n';
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
});

const patchRawUrl = usePatchRawUrl();
const isMobile = ref(false);

onMounted(() => {
    isMobile.value = window.innerWidth < 768;
    onClickOutside(hideMultiSelect);
    
    // 从本地存储恢复数据
    const savedData = localStorage.getItem('submitInfoFormData');
    if (savedData) {
        try {
            const parsedData = JSON.parse(savedData);
            Object.assign(formData.value, parsedData);
            if (parsedData.province) {
                selectedProvince.value = parsedData.province;
            }
        } catch (e) {
            console.error('Failed to parse saved form data', e);
        }
    }
});

onUnmounted(() => {
    offClickOutside(hideMultiSelect);
});


const emit = defineEmits(['submit', 'close'])

const formData = ref({
    name: '',
    company: '',
    phone: '',
    email: '',
    province: '',
    products: '',
    remark: '',
    agreed: false
})

const errors = ref({
    name: '',
    company: '',
    province: '',  // 新增省份验证字段
    products: '',
    phone: '',
    email: '',
    agreed: ''
})

const activeField = ref('')

const validateRules = {
    name: (value) => !!value.trim(),  // 新增name验证规则
    company: (value) => !!value.trim(), // 新增company验证规则
    phone: (value) => /^1[3-9]\d{9}$/.test(value),
    email: (value) => /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(value),
    province: (value) => !!value.trim(),  // 省份非空验证
    products: (value) => !!value.trim(), // 强化数组类型验证
    agreed: (value) => value === true,
}

const inputRefs = ref({})

const handleFocus = async (key) => {
    console.log(key)
    activeField.value = key
    await nextTick()
    inputRefs.value[key]?.focus()
}

const handleBlur = (key) => {
    activeField.value = ''
    validateField(key)
}

const validateField = (key) => {
    if (validateRules[key]) {
        errors.value[key] = validateRules[key](formData.value[key]) ? '' : getErrorMessage(key)
    }
}

const handleInput = (key) => {
    validateField(key)
}

const getErrorMessage = (key) => {
    return {
        name: '姓名不能为空',    // 新增name错误提示
        company: '公司名称不能为空', // 新增company错误提示
        phone: '请输入有效的手机号码',
        email: '请输入有效的邮箱地址',
        province: '请选择所在省份',
        products: '请选择产品',
        agreed: '请阅读并同意相关协议',
    }[key]
}

const inputFields = [
    { label: '姓名', key: 'name', type: 'input', placeholder: '请输入您的姓名' },
    { label: '公司名称', key: 'company', type: 'input', placeholder: '请输入公司全称' },
    { label: '联系电话', key: 'phone', type: 'input', placeholder: '请输入您的手机号码' },
    { label: '电子邮箱', key: 'email', type: 'input', placeholder: '请输入工作邮箱' },
    {
        label: '所在省份',
        key: 'province',
        type: 'select',
        options: [
            '北京市', '天津市', '河北省', '山西省', '内蒙古自治区',
            '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省',
            '浙江省', '安徽省', '福建省', '江西省', '山东省',
            '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区',
            '海南省', '重庆市', '四川省', '贵州省', '云南省',
            '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区',
            '新疆维吾尔自治区'
        ]
    },
    {
        label: '选择感兴趣的产品',
        key: 'products',
        type: 'multi-select',
        tabs: []
    },
    {
        label: '详细说明',
        key: 'remark',
        type: 'textarea',
        placeholder: '请输入详细说明'
    }
];


inputFields.map(item => {
    if (item.key === 'products') {
        item.tabs = props.data.productList
    }
})

const textLength = computed(() => formData.value.remark.length)

const formValid = computed(() => true)

const handleTextareaInput = (e: Event) => {
    const target = e.target as HTMLTextAreaElement
    if (target.value.length > 300) {
        formData.value.remark = target.value.slice(0, 300)
    }
}

const closeModal = () => {
    emit('close')
}

// 处理模态框背景点击事件
const handleModalBackgroundClick = (event) => {
    // 只有当点击的是模态框背景层本身时才关闭模态框
    // 如果点击的是表单内容，则不关闭
    if (event.target === event.currentTarget) {
        closeModal();
    }
}

const showsuccessMsgModal = ref(false);

const handleSubmit = async () => {
    Object.keys(formData.value).forEach(key => validateField(key))

    const hasErrors = Object.values(errors.value).some(error => error !== '')
    if (!hasErrors) {
        emit('submit', formData.value);

        // 保存数据到本地存储
        localStorage.setItem('submitInfoFormData', JSON.stringify(formData.value));

        let body = {
            name: formData.value.name,
            company: formData.value.company,
            phone: formData.value.phone,
            email: formData.value.email,
            province: formData.value.province,
            products: formData.value.products,
            remark: formData.value.remark,
            downloadedFile: props.data.downloadFile ? true: '',
            source: 'file_download' // 标识来源是文件下载页面
        }

        const result = await $axios('/customer/create', {
            body,
            method: 'post',
        });

        // 提交成功后触发文件下载
        if (props.data.downloadFile) {
            // 延迟一小段时间再下载，确保用户能看到成功提示
            setTimeout(() => {
                const file = props.data.downloadFile;
                const a = document.createElement('a');
                a.href = file.url;
                const fileName = file.name || 'download';
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            }, 1000);
        }

        showsuccessMsgModal.value = true;
    }
}


const selectedProvince = ref('')
const isOpen = ref(false)

function toggleDropdown(event) {
    event.stopPropagation(); // 阻止事件冒泡
    isOpen.value = !isOpen.value
}

function selectProvince(province, key) {
    formData.value[key] = province
    selectedProvince.value = province
    isOpen.value = false;
    handleBlur('province');
}

function hiadeProvince() {
    isOpen.value = false;
    if (activeField.value === 'province') {
        handleBlur('province')
    }
}

const activeMultiSelect = ref('')
const activeMultiSelectTab = ref(0)
const selectedProductsText = ref('');

function toggleMultiSelect(event, key) {
    event.stopPropagation()
    activeMultiSelect.value = activeMultiSelect.value === key ? '' : key
    activeMultiSelectTab.value = 0
}

function hideMultiSelect() {
    activeMultiSelect.value = '';
    if (activeField.value === 'products') {
        handleBlur('products')
    }
}

function onClickOutside(hideFn) {
    document.body.addEventListener('click', hideFn);
}

function offClickOutside(hideFn) {
    document.body.removeEventListener('click', hideFn);
}
function updateSelectedProducts(item) {
    const selected = item.tabs
        .flatMap(tab => tab.subTypes
            .flatMap(subType => subType.options
                .filter(opt => opt.checked)
            )
        )
    formData.value[item.key] = selected.map(opt => opt.name).join(', ') || '';
    selectedProductsText.value = selected.map(opt => opt.name).join(', ') || '请选择产品';
    validateField(item.key) // 选择后立即验证
}

</script>

<style lang="less" scoped>
.modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    touch-action: none;

    --Color-: #DF0428;
}

.submit-form {
    width: 708px;
    // height: 598px;
    background: #FFF;
    border-radius: 12px;
    padding: 32px;

    .title {
        max-width: 816px;
        color: var(--Color-, rgba(0, 0, 0, 0.8));
        font-family: "PingFang SC";
        font-size: 32px;
        font-style: normal;
        font-weight: var(--title-font-weight);
        line-height: 48px;
        margin: 0 auto;
    }

    .form-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        position: relative;

        title {
            font-family: 'PingFang SC';
            font-weight: var(--title-font-weight);
            font-size: 32px;
            color: rgba(0, 0, 0, 0.80);
            line-height: 48px;
            margin-bottom: 0;
        }

        .close-btn {
            display: block;
            width: 56px;
            height: 56px;
            background: url('/assets/imgs/icon_close.svg') no-repeat center center;
            background-size: 18px 18px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 56px;
            cursor: pointer;
            position: absolute;
            top: 0;
            right: 0;
        }
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        margin-bottom: 24px;

        .textarea-item {
            grid-column: span 2;
        }
    }

    .input-container {
        width: 310px;
        height: 58px;
        position: relative;
        // z-index: 1;
        border: 1px solid #D8D8D8;
        border-radius: 12px;
        padding: 28px 16px 7px;

        &.focused {
            border: 1px solid #1677FF;
        }

        &.error {
            border: 1px solid #DF0428;
        }

        .input {
            color: rgba(0, 0, 0, 0.80);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: var(--sub-title-font-weight);
            line-height: 21px;
            border: none;
            outline: none;
            width: 100%;

            &::placeholder {
                color: rgba(0, 0, 0, 0.43);
                font-weight: 300;
            }
        }

        &.error .input {
            color: #DF0428;
        }

        .label {
            display: flex;
            z-index: 2;
            background: white;
            padding: 0;
            transform: none;
            color: rgba(0, 0, 0, 0.65);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            background-color: transparent;
            position: absolute;
            top: 18px;
            left: 16px;
            transition: all 0.3s ease;
        }

        &.active .label {
            font-size: 10px;
            font-weight: 400;
            line-height: 15px;
            top: 8px;
        }

        .required {
            color: #DF0428;
        }

    }

    .select-container {
        position: relative;
        padding: 0;

        color: rgba(0, 0, 0, 0.65);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 21px;

        &::after {
            content: '';
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            background: url('../assets/imgs/chevron-down.svg') no-repeat center;
            pointer-events: none;
        }


        .dropdown {
            position: relative;
            cursor: pointer;
            user-select: none;
            background-color: #fff;
            margin: 0;
        }

        .selected {
            display: block;
            justify-content: space-between;
            align-items: center;
            height: 54px;
            padding: 28px 16px 5px;
            color: rgba(0, 0, 0, 0.8);
            font-weight: var(--sub-title-font-weight);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            list-style: none;
            padding: 0;
            margin: 7px 0 0;
        }

        .options .selected-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: rgba(0, 0, 0, 0.8);
            font-weight: var(--sub-title-font-weight);
        }

        .options li {
            padding: 10px;
            cursor: pointer;
        }

        .options li:hover {
            background-color: #f2f2f2;
        }

        .options li.selected-item {
            background-color: #f2f2f2;
            // font-weight: bold;
        }

    }

    .error-message {
        color: #DF0428;
        margin-right: 1px;
        font-family: "PingFang SC";
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        margin-top: 4px;
    }

    .form-textarea {
        position: relative;
        margin-bottom: 32px;
        min-height: 124px;

        .input-container {
            height: auto;
            z-index: 0;
            padding: 18px 16px 12px;
            width: 100%;
        }

        .textarea-container {
            width: 100%;
            padding: 0 0 24px;
            position: relative;
            line-height: 0;
        }

        textarea {
            width: 100%;
            height: 63px;
            border: none;
            font-size: 14px;
            outline: none;
            color: rgba(0, 0, 0, 0.80);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            resize: none;
            padding: 0;

            &::placeholder {
                color: rgba(0, 0, 0, 0.43);
            }
        }

        .word-count {
            position: absolute;
            right: 16px;
            bottom: 4px;
            font-size: 12px;
            font-family: "PingFang SC";
            font-size: 10px;
            font-weight: 400;
            line-height: 15px;
            color: rgba(0, 0, 0, 0.43);
        }
    }

    .form-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .agree-check {
            display: flex;
            align-items: center;
            line-height: 0;
            position: relative;

            .agree-text {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.65);
                font-family: "PingFang SC";
                font-weight: 400;
                line-height: 18px;

                .agree-doc {
                    color: #1677FF;
                    margin: 0 4px;
                    cursor: pointer;
                }
            }

            .agreed-error {
                position: absolute;
                left: 0;
                top: 100%;
            }
        }

        .submit-btn {
            display: inline-flex;
            height: 56px;
            padding: 12px 32px;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            border: none;
            cursor: pointer;
            border-radius: 12px;
            background: linear-gradient(180deg, #000 -42.86%, #555 100%);
            color: rgba(255, 255, 255, 0.97);
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;

            &:disabled {
                // background: #D9D9D9;
                cursor: not-allowed;
            }

            &:not(:disabled):hover {
                opacity: 0.9;
            }
        }
    }
}

.custom-checkbox-container {
    display: inline-flex;
    align-items: center;
    line-height: 0;
    margin-right: 8px;
    position: relative;

    .custom-checkbox {
        opacity: 0;
        position: absolute;
        width: 100%;
        height: 100%;

        &+span::before {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 1px solid #D8D8D8;
            border-radius: 4px;
            vertical-align: middle;
        }

        &:checked+span::before {
            background: #1677FF url("data:image/svg+xml;charset=UTF-8,%3csvg width='12' height='10' viewBox='0 0 12 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M1 5.5L4.5 9L11 1' stroke='white' stroke-width='2' stroke-linecap='round'/%3e%3c/svg%3e") no-repeat center;
            background-size: 8px 8px;
            border-color: #1677FF;
            border: none;
        }
    }


}


.multi-select-container {
    .multi-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        margin-top: 5px;
        width: 500px;
        height: 352px;
        background: #FFF;
        border-radius: 12px;
        box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05),
            0px 8px 10px 1px rgba(0, 0, 0, 0.06),
            0px 5px 5px -3px rgba(0, 0, 0, 0.10);
        z-index: 1000;
        overflow: auto;

        .tab-bar {
            display: flex;
            border-bottom: 1px solid #D8D8D8;
            gap: 24px;

            .tab-item {
                display: inline-flex;
                height: 56px;
                padding: 0 24px;
                justify-content: center;
                align-items: center;
                // margin-right: 24px;
                color: rgba(0, 0, 0, 0.80);
                text-align: center;
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 27px;
                cursor: pointer;
                position: relative;
                white-space: nowrap;

                &.active {
                    color: #DF0428;
                    font-weight: var(--title-font-weight);

                    // border-bottom: 1px solid #DF0428;
                    &::after {
                        content: '';
                        display: block;
                        width: 100%;
                        height: 1px;
                        background: #DF0428;
                        position: absolute;
                        bottom: -1px;
                        left: 0;
                    }
                }
            }
        }

        .tab-content {
            padding: 24px;
            height: calc(100% - 56px);
            overflow-y: auto;

            .sub-type {
                margin-bottom: 25px;
            }

            .sub-type-title {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 12px;
                font-weight: var(--title-font-weight);
                line-height: 18px;
                margin-bottom: 9px;
            }

            .option {
                display: inline-flex;
                align-items: center;
                height: 18px;
                margin-right: 34px;
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 12px;
                font-weight: var(--sub-title-font-weight);
                line-height: 18px;
            }


        }
    }
}

@media (max-width: 768px) {
    .submit-form {
        width: 100%;
        height: 100%;
        border-radius: 0;
        padding: 13px 16px 16px;

        .title {
            font-size: 18px;
            line-height: 27px;
            margin-bottom: 0;
        }

        .form-header .close-btn {
            width: 40px;
            height: 40px;
            background-color: #555;
            top: initial;
            bottom: 0;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 16px;
            margin-bottom: 0;

            .form-item {
                width: 100%;
            }

            .textarea-item {
                grid-column: span 1;
            }

            .input-container {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                height: 40px;
                padding: 0 12px;
                border-radius: 6px;

                .label {
                    position: initial;
                    white-space: nowrap;
                    margin-right: 12px;
                }

                &.active .label {
                    font-size: 14px;
                    line-height: 21px;
                    white-space: nowrap;
                    flex: 0 0 auto;
                }

                .input {
                    text-align: right;
                }
            }

            .error-message {
                text-align: right;
            }
        }

        .select-container {
            .selected {
                padding: 0 40px;
                width: 100px;
                height: 40px;
                line-height: 40px;
                flex: 1;
                text-align: right;
            }

            .dropdown {
                position: absolute;
                width: 150px;
                top: 100%;
                right: 0;
                margin-top: -2px;
            }
        }

        .form-textarea {
            height: 107px;
            min-height: 107px;
            margin-bottom: 0;

            .input-container {
                padding: 13px 12px;
                height: 107px;
            }

            .input-container .label {
                display: none;
            }
        }

        .multi-select-container .multi-dropdown {
            width: 100%;

            .custom-checkbox-container {
                padding: 12px 8px 12px 12px;
                margin-right: 0;
                margin-left: -12px;
            }
        }

        .form-footer {
            display: flex;
            flex-direction: column;
            align-items: stretch;

            .agree-check {
                height: 18px;
                margin: 12px 0 0;
            }

            .submit-btn {
                width: 100%;
                height: 40px;
                margin: 24px 0 24px;
                border-radius: 6px;
            }
        }
    }

    .agree-check .custom-checkbox-container {
        padding: 12px 8px 12px 12px;
        margin-right: 0;
        margin-left: -12px;
    }
}
</style>
