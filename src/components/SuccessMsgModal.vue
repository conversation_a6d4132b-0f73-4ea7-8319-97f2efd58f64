<template>
    <div v-if="show" class="modal-mask" @click.self="handleClose">
        <div class="modal-container">
            <span class="close-btn" @click="handleClose">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M0.646447 0.646447C0.841709 0.451184 1.15829 0.451184 1.35355 0.646447L15.3536 14.6464C15.5488 14.8417 15.5488 15.1583 15.3536 15.3536C15.1583 15.5488 14.8417 15.5488 14.6464 15.3536L0.646447 1.35355C0.451185 1.15829 0.451185 0.841709 0.646447 0.646447Z"
                        fill="#090909" fill-opacity="0.43" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M15.3536 0.646447C15.1583 0.451184 14.8417 0.451184 14.6464 0.646447L0.646446 14.6464C0.451185 14.8417 0.451185 15.1583 0.646446 15.3536C0.841709 15.5488 1.15829 15.5488 1.35355 15.3536L15.3536 1.35355C15.5488 1.15829 15.5488 0.841709 15.3536 0.646447Z"
                        fill="#090909" fill-opacity="0.43" />
                </svg>
            </span>
            <div class="icon-success">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
                    <circle cx="32" cy="32" r="31" stroke="#2BA471" stroke-width="2" />
                    <path d="M23 33.1429L29.5818 39.8291C29.6694 39.9181 29.8161 39.906 29.8879 39.8038L41 24"
                        stroke="#2BA471" stroke-width="2" stroke-linecap="round" />
                </svg>
            </div>

            <div class="content">
                <h3 class="title">提交成功</h3>
                <p class="description">提交成功，您的信息已发送给我们</p>
            </div>
        </div>
    </div>
</template>

<script setup>
defineProps({
    show: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['close']);

const handleClose = () => {
    emit('close');
};
</script>

<style scoped lang="less">
.modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}

.modal-container {
    width: 528px;
    height: 288px;
    position: relative;
    background: white;
    padding: 70px 24px;
    border-radius: 12px;
    text-align: center;
}

.close-btn {
    position: absolute;
    right: 24px;
    top: 24px;
    cursor: pointer;
    display: flex;
    padding: 8px;
    transition: opacity 0.3s;
}

.close-btn:hover {
    opacity: 0.8;
}

.close-btn svg {
    width: 16px;
    height: 16px;
}

.close-btn:hover {
    color: #333;
}

.icon-success {
    width: 64px;
    height: 64px;
    background: #fff;
    border-radius: 50%;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-success svg {
    width: 100%;
    height: 100%;
}

.title {
    font-size: 20px;
    margin-bottom: 12px;
    color: #333;
    color: rgba(0, 0, 0, 0.80);
    font-family: "PingFang SC";
    font-size: 18px;
    font-weight: var(--title-font-weight);
    line-height: 27px;
    margin-bottom: 12pt;
}

.description {
    color: rgba(0, 0, 0, 0.65);
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
}

@media (max-width: 768px) {
    .modal-container {
        width: 280px;
        height: 180px;
        padding: 42px 24px;

        .icon-success {
            width: 32px;
            height: 32px;
            margin-bottom: 12px;
        }

        .title {
            font-size: 16px;
            line-height: 24px;
            margin-bottom: 8px;
        }

        .close-btn {
            padding: 12px;
            top: 0;
            right: 0;
            svg {
                width: 12px;
                height: 12px;
            }
        }
    }
}
</style>