<template>
    <div class="support-doc">
        <div class="title" :style="{ color: data.textColor }">{{data.title}}</div>
        <div class="tab-bar">
            <div class="tab-item" v-for="(tab, index) in data.list" :key="index" @click="activeTab = index"
                :class="{ 'active': activeTab === index }">
                {{ tab.name }}
            </div>
        </div>
        <div class="doc-content">
            <pre class="doc-text" v-html="curContent"></pre>
        </div>
    </div>
</template>
<script lang="ts" setup>
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                list: [

                ]
            }
        }
    }
})

const activeTab = ref(0);

const curContent = computed(() => props.data.list[activeTab.value].content);

</script>
<style lang="less" scoped>
.support-doc {
    max-width: 1440px;
    // max-height: 1626px;
    background-color: #fff;
    padding: 100px 0;
    margin: 0 auto;
    margin-bottom: var(--component-mb);

    .title {
        max-width: 816px;
        text-align: center;
        color: rgba(0, 0, 0, 0.80);
        font-family: "PingFang SC";
        font-size: 48px;
        font-style: normal;
        font-weight: var(--title-font-weight);
        line-height: 72px;
        margin: 0 auto 40px;
    }

    .tab-bar {
        max-width: 1440px;
        margin: 0 auto 40px;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #D8D8D8;
        gap: 40px;

        .tab-item {
            display: inline-flex;
            height: 56px;
            padding: 0 24px;
            justify-content: center;
            align-items: center;
            color: rgba(0, 0, 0, 0.80);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 18px;
            font-weight: var(--sub-title-font-weight);
            line-height: 27px;
            cursor: pointer;
            position: relative;
            white-space: nowrap;

            &.active {
                color: #DF0428;
                font-weight: var(--title-font-weight);

                &::after {
                    content: '';
                    display: block;
                    width: 100%;
                    height: 1px;
                    background: #DF0428;
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                }
            }
        }
    }

    .doc-content {
        padding: 0 20px;
        color: rgba(0, 0, 0, 0.65);
        text-align: justify;
        font-family: "PingFang SC";
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;

        .doc-text {
            text-wrap: auto;
        }
    }
}

@media (max-width: 768px) {
    .support-doc {
        margin: 0 16px;
        border-radius: 12px;
        padding: 32px 0;
        margin-bottom: var(--component-mb);

        .title {
            font-size: 18px;
            line-height: 21px;
        }

        .tab-bar {
            flex-wrap: wrap;
            justify-content: flex-start;
            margin: 0 auto 12px;
            gap: 12px;

            .tab-item {
                height: 40px;
                padding: 0 10px;
                font-size: 14px;
                line-height: 21px;
            }
        }

        .doc-content {
            padding: 0 16px;
        }
    }
}
</style>