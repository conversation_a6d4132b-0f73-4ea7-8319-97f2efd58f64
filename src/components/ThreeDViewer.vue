<template>
    <div class="viewer">
        <div class="viewer-desc">
            <div class="bold-text">{{data.title}}</div>
            <div class="normal-text">{{data.desc}}</div>
        </div>
        <!-- <i class="icon-drag-view"><span class="icon-text">360°</span></i> -->
        <div class="viewer-bottom">{{ data.bottomNote }}</div>
        <div class="iframe-contaienr">
            <iframe class="viewer-iframe" frameborder="0" allowfullscreen mozallowfullscreen="true"
                webkitallowfullscreen="true" allow="autoplay; fullscreen; xr-spatial-tracking" xr-spatial-tracking
                execution-while-out-of-viewport execution-while-not-rendered web-share
                :src="data.modelUrl"> </iframe>
        </div>
    </div>
</template>
<script setup lang='ts'>
const props = defineProps({
    data: {
        type: Object,
        default: () => []
    }
})

</script>
<style lang="less" scoped>
.viewer {
    display: flex;
    width: 100%;
    max-width: 1440px;
    max-height: 600px;
    margin: 0 auto;
    border-radius: 12px;
    aspect-ratio: 1440/600;
    position: relative;
    overflow: hidden;
    margin-bottom: var(--component-mb);

    .viewer-desc {
        height: 40px;
        position: absolute;
        top: 50%;
        left: 40px;
        transform: translateY(-40px);
        z-index: 1;
        user-select: none;

        .bold-text {
            color: rgba(0, 0, 0, 0.80);
            font-family: "PingFang SC";
            font-size: 32px;
            font-weight: var(--title-font-weight);
            line-height: 48px;
            margin-bottom: 16px;
        }

        .normal-text {
            color: rgba(0, 0, 0, 0.65);
            font-family: "PingFang SC";
            font-size: 24px;
            font-weight: 400;
            line-height: 36px;
        }
    }

    .icon-drag-view {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 56px;
        height: 56px;
        background-color: rgba(0, 0, 0, 0.8);
        border-radius: 100%;
        position: absolute;
        top: 60%;
        right: 10%;
        z-index: 1;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;

        .icon-text {
            display: inline-block;
            width: 26px;
            color: #FFF;
            font-family: "PingFang SC";
            font-size: 12px;
            font-weight: 300;
            line-height: 17px;
            margin-top: 14px;
            font-style: normal;
            margin-left: 4px;
            margin-top: 14px;

        }

        &::before {
            content: '';
            mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4" fill="none"> <path d="M1.08984 3.30078L2.5999 2.32112C2.69256 2.261 2.71895 2.13715 2.65883 2.04448L1.67917 0.53443" stroke="white" stroke-linecap="round"/> </svg>');
            mask-size: 4px 4px;
            mask-repeat: no-repeat;
            width: 4px;
            height: 4px;
            background-color: #FFF;
            position: absolute;
            top: 34px;
            transform: translateX(-1px);
        }

        &::after {
            content: '';
            mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="30" height="10" viewBox="0 0 30 10" fill="none"><path d="M2.52653 1C1.55053 1.75158 1 2.603 1 3.50504C1 6.40584 6.69333 8.78319 13.9213 9M27.4735 1C28.4495 1.75158 29 2.603 29 3.50504C29 5.9046 25.1042 7.94597 19.6667 8.70253" stroke="white" stroke-linecap="round"/></svg>');
            mask-size: 30px 10px;
            mask-repeat: no-repeat;
            width: 30px;
            height: 10px;
            background-color: #FFF;
            position: absolute;
            top: 27px;
        }
    }

    &:hover .icon-drag-view {
        visibility: visible;
        opacity: 1;
    }

    .viewer-bottom {
        width: 100%;
        text-align: center;
        color: #666;
        font-family: "PingFang SC";
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        position: absolute;
        bottom: 12px;
        z-index: 1;
    }

    .iframe-contaienr {
        width: calc(100% + 140px);
        height: 100%;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        overflow: hidden;

        .viewer-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    }
}

@media (max-width: 768px) {
    .viewer {
        width: calc(100% - 32px);
        aspect-ratio: 361/323;
        // margin-bottom: 40px;
        border-radius: 6px;

        .viewer-desc {
            width: 100%;
            text-align: center;
            position: initial;
            z-index: 1;
            user-select: none;
            transform: none;
            padding-top: 24px;
        }

        .viewer-desc .bold-text {
            font-size: 18px;
            line-height: 27px;
            margin-bottom: 12px;
        }

        .viewer-desc .normal-text {
            font-size: 14px;
            line-height: 21px;
        }

        .viewer-bottom {
            font-size: 10px;
            line-height: 15px;
            bottom: 28px;
        }
    }
}
</style>