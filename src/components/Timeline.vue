<template>
    <div class="timeline">
        <div class="big-text">{{ data.timelineList[curItemIndex].title }}</div>
        <div class="timeline-content-block">
            <div class="top-block">
                <div class="title">
                    <div class="title-text">{{ data.title }}</div>
                </div>
                <div class="desc">
                    <img :src="$patchRawUrl(data.timelineList[curItemIndex].img.url)" alt="" class="desc-img desc-left"
                        v-if="data.timelineList[curItemIndex].img">
                    <div class="desc-right">
                        <div class="desc-title">{{ data.timelineList[curItemIndex].title }}</div>
                        <div class="desc-content" v-html="data.timelineList[curItemIndex].desc"></div>
                    </div>
                </div>
            </div>
            <div class="bottom-block swiper" ref="bottomBlock">
                <div class="swiper-wrapper">
                    <div class="item swiper-slide" :class="{ active: index === curItemIndex }" @click="handleClick(index)"
                        v-for="(item, index) in data.timelineList" :key="index">
                        <span class="item-key">{{ item.title }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {
                title: '',
                timelineList: [
                    {
                        title: '',
                        desc: ''
                    }
                ]
            }
        }
    }
})

const curItemIndex = ref(0);
const bottomBlock = ref(null);

const handleClick = (index) => {
    curItemIndex.value = index;
    // 获取Swiper实例并滑动到对应索引
    if (bottomBlock.value?.swiper) {
        bottomBlock.value.swiper.slideTo(index, 500); // 500ms滑动动画时长
    }
};

useSwiper(bottomBlock, {
    slidesPerView: 'auto',
    spaceBetween: 0,
    // loop: true
});

</script>

<style scoped lang="less">
@font-face {
    font-family: DIN-Pro;
    src: url(../assets/fonts/DINPro-Black.otf);
}

.timeline {
    width: 100%;
    padding: 100px 20px 100px;
    margin-bottom: var(--component-mb);
    background-color: #fff;
    position: relative;

    .big-text {
        display: flex;
        align-items: center;
        font-family: DIN-Pro;
        font-size: 194px;
        height: 155px;
        color: rgba(0, 0, 0, 0.80);
        padding-bottom: 18px;
        opacity: 0.06;
        position: absolute;
        top: 100px;
        right: 238px;
    }

    .top-block {
        max-width: 1440px;
        display: flex;
        flex-direction: column;
        margin: 0 auto 56px;

        .title {
            .title-text {
                height: 72px;
                color: #DF0428;
                font-family: "PingFang SC";
                font-size: 48px;
                font-weight: var(--title-font-weight);
                line-height: 72px;
                margin-bottom: 80px;
            }
        }

        .desc {
            width: 100%;
            min-height: 319px;
            display: flex;
            position: relative;
            justify-content: center;
            margin: 0 auto 80px;

            .desc-img {
                width: 646px;
                height: 319px;
                border-radius: 12px;
                object-fit: cover;
                margin-right: 40px;
            }

            .desc-right {
                width: 756px;

                .desc-title {
                    color: rgba(0, 0, 0, 0.80);
                    font-family: "PingFang SC";
                    font-size: 32px;
                    font-weight: var(--title-font-weight);
                    line-height: 48px;
                    margin-top: 45px;
                    margin-bottom: 24px;
                }

                .desc-content {
                    color: rgba(0, 0, 0, 0.65);
                    font-family: "PingFang SC";
                    font-size: 24px;
                    font-style: normal;
                    font-weight: 300;
                    line-height: 36px;
                    padding-left: 20px;

                    p {
                        margin-bottom: 24px;
                    }
                }
            }
        }
    }

    .bottom-block {
        max-width: 1440px;
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        padding: 0 40px;
        border-top: 1px solid #D8D8D8;
        overflow: hidden;

        .item {
            width: 64px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 7px 10px;
            cursor: pointer;
            position: relative;
            margin-right: 80px;

            .item-key {
                color: rgba(0, 0, 0, 0.80);
                font-family: "PingFang SC";
                font-size: 18px;
                font-weight: 400;
                line-height: 27px;
            }

            &.active {
                border-top: 1px solid #DF0428;

                .item-key {
                    font-weight: var(--title-font-weight);
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .timeline {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 32px 0;
        margin-bottom: var(--component-mb);

        .big-text {
            display: none;
        }

        .timeline-content-block{
            display: flex;
            flex-direction: column-reverse;
        }

        .top-block {
            width: 100%;
            display: flex;
            flex-direction: column;
            margin: 0 auto;
            padding: 0 16px;

            .title {
                .title-text {
                    width: 100%;
                    height: 21px;
                    font-size: 18px;
                    line-height: 21px;
                    margin-bottom: 0;
                    text-align: center;
                }
            }

            .desc {
                width: 100%;
                min-height: initial;
                display: flex;
                flex-direction: column-reverse;

                position: relative;
                justify-content: center;
                margin: 0 auto;

                .desc-img {
                    width: 100%;
                    height: initial;
                    aspect-ratio: 361/200;
                    border-radius: 12px;
                    object-fit: cover;
                    margin-right: 40px;
                }

                .desc-right {
                    width: 100%;
                    margin-bottom: 12px;

                    .desc-title {
                        font-size: 14px;
                        line-height: 21px;
                        margin-top: 12px;
                        margin-bottom: 12px;
                        text-align: center;
                    }

                    .desc-content {
                        font-size: 14px;
                        line-height: 21px;
                        padding-left: 16px;

                        p {
                            margin-bottom: 12px;
                        }
                    }
                }
            }
        }

        .bottom-block {
            width: 100%;
            height: 40px;
            max-width: 1440px;
            display: flex;
            justify-content: space-between;
            margin: 0 auto 24px;
            padding: 0 40px 0 0;
            border-top: 1px solid #D8D8D8;
            overflow: hidden;

            .item {
                width: 54px;
                height: 40px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 0 10px;
                cursor: pointer;
                position: relative;
                margin-right: 24px;

                .item-key {
                    font-size: 14px;
                    line-height: 27px;
                }

                &.active {
                    border-top: 1px solid #DF0428;

                    .item-key {
                        font-weight: var(--title-font-weight);
                    }
                }
            }
        }
    }
}
</style>