<template>
    <div class="video-modal">
        <div class="modal-content">
            <iframe :src="videoUrl" scrolling="no" border="0" frameborder="no" framespacing="0"
                allowfullscreen="true"></iframe>
        </div>
        <div class="close-btn" @click="closeModal">
            <i class="icon-x"></i>
        </div>
    </div>
</template>
<script setup lang='ts'>
const props = defineProps({
    videoUrl: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['close']);
const closeModal = () => {
    emit('close');
};

</script>

<style scoped lang="less">
.video-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;

    .modal-content {
        position: relative;
        width: 80%;
        max-width: 1440px;
        padding-top: 47.32%;
        margin: 0 80px;
        /* 16:9 aspect ratio */

        iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

    }

    .close-btn {
        width: 56px;
        height: 56px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        position: absolute;
        top: 40px;
        right: 40px;
        background: none;
        border: none;
        color: white;
        cursor: pointer;
    }
}

@media (max-width: 768px) {
    .video-modal {
        .modal-content {
            margin: 0 16px;
            padding-top: 45.32%;
        }
    }
}
</style>