<template>
    <div class="input-container" @click="handleContainerClick();handleFocus()" :class="{
        'focused': active,
        'error': error,
        'active': active || modelValue
    }">
        <label class="label">
            <span class="required" v-if="required">*</span>
            {{ label }}
        </label>
        <input class="input" type="text" :placeholder="placeholder" :value="modelValue"
            @input="$emit('update:modelValue', $event.target.value)" @focus="handleFocus" @blur="handleBlur"
            ref="inputRef" />
        <div v-if="error" class="error-message">{{ error }}</div>
    </div>
</template>

<script setup>
const props = defineProps({
    label: String,
    placeholder: String,
    modelValue: String,
    error: String,
    required: Boolean
});

const emit = defineEmits(['update:modelValue', 'focus', 'blur']);
const active = ref(false);
const inputRef = ref(null);

const handleFocus = () => {
    active.value = true;
    emit('focus');
};

const handleBlur = () => {
    console.log('inputRef', inputRef.value?.value);
    active.value = false;
    emit('blur');
};

const handleContainerClick = () => {
    inputRef.value?.focus();
};
</script>
<style lang="less" scoped>
.input-container {
    width: 100%;
    height: 58px;
    position: relative;
    z-index: 1;
    border: 1px solid #D8D8D8;
    border-radius: 12px;
    padding: 28px 16px 7px;

    &.focused {
        border: 1px solid #1677FF;
    }

    &.error {
        border: 1px solid #DF0428;
    }

    .input {
        display: none;
        color: rgba(0, 0, 0, 0.80);
        font-family: "PingFang SC";
        font-size: 14px;
        font-weight: var(--sub-title-font-weight);
        line-height: 21px;
        border: none;
        outline: none;
        width: 100%;

        &::placeholder {
            color: rgba(0, 0, 0, 0.43);
            font-weight: 300;
        }
    }

    &.active {
        .input {
            display: block;
        }
    }

    &.error .input {
        color: #DF0428;
    }

    .label {
        display: flex;
        z-index: 2;
        background: white;
        padding: 0;
        transform: none;
        color: rgba(0, 0, 0, 0.65);
        font-family: "PingFang SC";
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;
        background-color: transparent;
        position: absolute;
        top: 18px;
        left: 16px;
        transition: all 0.3s ease;
    }

    &.active .label {
        font-size: 10px;
        font-weight: 400;
        line-height: 15px;
        top: 8px;
    }

    .required {
        color: #DF0428;
    }

}

.select-container {
    position: relative;
    padding: 0;

    color: rgba(0, 0, 0, 0.65);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;

    &::after {
        content: '';
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        background: url('../assets/imgs/chevron-down.svg') no-repeat center;
        pointer-events: none;
    }


    .dropdown {
        position: relative;
        cursor: pointer;
        user-select: none;
        background-color: #fff;
        margin: 0;
    }

    .selected {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 28px 16px 5px;
        color: rgba(0, 0, 0, 0.8);
        font-weight: var(--sub-title-font-weight);
    }

    .options {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ccc;
        border-radius: 8px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        list-style: none;
        padding: 0;
        margin: 4px 0 0;
    }

    .options .selected-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .options li {
        padding: 10px;
        cursor: pointer;
    }

    .options li:hover {
        background-color: #f2f2f2;
    }

    .options li.selected {
        background-color: #e6f7ff;
        font-weight: bold;
    }

}

.error-message {
    color: #DF0428;
    margin-right: 1px;
    font-family: "PingFang SC";
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    margin-top: 4px;
}
</style>