import AOS, { AOSOptions } from 'aos';

export default function useAos(options?: AOSOptions) {
  const resetAos = () => {
    document.querySelectorAll('[data-aos]')?.forEach((el) => {
      el.classList.remove('aos-init');
      el.classList.remove('aos-animate');

      Reflect.deleteProperty(el, 'animated');
    });
  };

  const initAos = () => {
    AOS.init({
      easing: 'ease',
      duration: 700,
      ...options,
    });
  };

  onBeforeUnmount(resetAos);

  useDelayMounted(initAos);
}
