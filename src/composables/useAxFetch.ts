import { unwrap } from '@/axios';
import type { UseFetchOptions } from '#app';

export default function useAxFetch<R = API.Any>(url: string, options?: UseFetchOptions<R>) {
  const config = useRuntimeConfig();
  const baseURL = config.public.NUXT_API_BASE;

  return useFetch(url, {
    baseURL,
    key: url,
    initialCache: false,
    transform: (r) => unwrap(r as any),
    ...options,
    query: {
      ...options?.query,
      _: Date.now(),
    },
  });
}
