function createBackdrop() {
  const e = document.createElement('div');

  e.style.cssText = `
position: fixed;
top: 0;
left: 0;
right: 0;
bottom: 0;
z-index: 200;
background-color: rgba(0, 0, 0, .3);
`;
  return e;
}

function mountBackdrop(el: HTMLElement) {
  if (el) {
    document.body.appendChild(el);
  }
}

function unmountBackdop(el: HTMLDivElement) {
  if (el) {
    el.remove();
  }
}

export default function useBackdrop() {
  const el = ref<HTMLDivElement>();

  const show = () => {
    if (!el.value) {
      el.value = createBackdrop();

      mountBackdrop(el.value);
    }

    el.value.style.display = '';
  };

  const hide = () => {
    if (el.value) {
      el.value.style.display = 'none';
    }
  };

  onBeforeUnmount(() => unmountBackdop(el.value));

  return {
    hide,
    show,
  };
}
