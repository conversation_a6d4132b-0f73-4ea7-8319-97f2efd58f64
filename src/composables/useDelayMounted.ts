import AOS from 'aos';

export type Hook = () => any;

export default function useDelayMounted(hook: Hook | Hook[], opts?: { delay?: number }) {
  const callHooks = () => {
    const hooks = Array.isArray(hook) ? hook : [hook];

    for (const _hook of hooks) {
      _hook();
    }
  };

  const onDelayMounted = () => {
    callHooks();
  };

  const res = onMounted(() => {
    setTimeout(onDelayMounted, opts?.delay ?? 60);
  });

  return res;
}
