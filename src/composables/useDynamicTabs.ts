import { Ref, WatchOptions, WatchSource } from 'vue';

export type DynamicTabs = { tab?: HTMLElement, pane?: HTMLElement };
export type SelectFn = () => DynamicTabs;
export type MaybeEle = string | Ref<string> | HTMLElement | Ref<HTMLElement> | SelectFn;

export type Options = WatchOptions & {
  container: MaybeEle;
  handler?: (tabs: DynamicTabs) => void;
};

const isStr = (v: unknown): v is string => typeof v === 'string';
const isFn = (v: unknown): v is Function => typeof v === 'function';
const isEl = (v: unknown): v is HTMLElement => v && (v as HTMLElement).nodeType === Node.ELEMENT_NODE;

function getEl(el: MaybeEle): DynamicTabs {
  if (isStr(el)) {
    return {
      tab: document.querySelector(`${el} .nav-link`),
      pane: document.querySelector(`${el} .tab-pane`),
    };
  }

  if (isEl(el)) {
    return {
      tab: el?.querySelector('.nav-link'),
      pane: el?.querySelector('.tab-pane'),
    };
  }

  if (isFn(el)) {
    return el();
  }

  return getEl(el.value);
}

export default function useDynamicTabs<T extends object>(source: WatchSource<T>, options: Options) {
  const handler = async () => {
    if (process.server) return;

    await nextTick();

    const { tab, pane } = getEl(options.container);

    if (options.handler) {
      options.handler({ tab, pane });
    } else {
      tab?.classList.add('active');

      pane?.classList.add('show', 'active');
    }
  };

  const unwatch = watch(source, handler, { immediate: true, ...options });

  useDelayMounted(handler);

  onUnmounted(unwatch);
}
