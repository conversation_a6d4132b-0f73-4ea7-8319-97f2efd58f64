import type { Ref } from 'vue';

export type Options<R extends API.Any = API.Any> = {
  manual?: boolean;
  pageSize?: number;
  defaultPage?: number;
  params?: API.Any | (() => API.Any);
  transform?: (data: any) => R[];
};

const isFn = <A>(v: unknown): v is (() => A) => typeof v === 'function';

const defaultOptions: Options = {
  pageSize: 20,
};

export default function useInfiniteLoad<R extends API.Any = API.Any>(url: string, options?: Options<R>) {
  const opts = Object.assign({}, defaultOptions, options);
  const total = ref(0);
  const error = ref<Error>();
  const loading = ref(false);
  const refreshing = ref(false);
  const data = ref([]) as Ref<R[]>;
  const pageSize = ref(opts.pageSize);
  const page = ref(opts.defaultPage ?? 1);
  const loaded = ref(false);

  const noData = computed(() => {
    return !loading.value && !refreshing.value && loaded.value && total.value === 0;
  });

  const noMore = computed(() => {
    return !loading.value && !refreshing.value && loaded.value && data.value.length >= total.value;
  });

  const service = (paging: API.PagingParams) => {
    const params = isFn<API.Any>(opts.params) ? opts.params() : opts.params;

    return $axios<API.PagingRes<R>>(url, {
      params: {
        ...params,
        ...paging,
      },
    });
  };

  const transformList = (list: any) => {
    if (opts.transform) {
      return opts.transform(list);
    }

    return list;
  };

  const fetchData = async (p: number, clear?: boolean) => {
    error.value = null;
    try {
      const params = {
        page: p,
        limit: pageSize.value,
      };

      const res = await service(params);

      if (res) {
        const list = transformList(res.list ?? []);

        total.value = res.total ?? 0;

        data.value = clear ? list : data.value.concat(list);

        if (data.value.length < total.value) {
          page.value = p;
        }
      }

      loaded.value = true;
    } catch(e) {
      error.value = e;
    }
  }

  const load = async () => {
    if (loading.value || refreshing.value || noData.value || noMore.value) return;

    loading.value = true;

    await fetchData(loaded.value ? page.value + 1 : (opts.defaultPage ?? 1));

    loading.value = false;
  };

  const refresh = async () => {
    if (refreshing.value || loading.value) return;

    loaded.value = false;
    refreshing.value = true;

    await fetchData(1, true);

    refreshing.value = false;
  };

  if (!loaded.value && !opts.manual) {
    load();
  }

  return {
    data,
    page,
    error,
    total,
    noData,
    noMore,
    loading,
    pageSize,
    refreshing,
    load,
    refresh,
  };
}
