import { Ref, onUnmounted } from 'vue'

export function useIntersectionTrigger(options: {
  target: Ref<HTMLElement | null>
  onIntersect: () => void
  threshold?: number
}) {
  const { target, onIntersect, threshold = 0.1 } = options
  let observer: IntersectionObserver | null = null



  const init = () => {
    if (target.value) {
      observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            onIntersect()
          }
        })
      }, { threshold })

      observer.observe(target.value)
    }
  }

  useDelayMounted(init);

  onUnmounted(() => {
    observer?.disconnect()
  })
}