import { usePlans } from '~/api/plan';
import { useSectons } from '~/api/common';
import { useProducts } from '~/api/product';

export default function useNavs() {
  const { data: plans } = usePlans();
  const { data: sections } = useSectons();
  const { data: products } = useProducts();

  const filterProducts = (sid: number) => {
    return products.value?.filter((item) => item.sectionId === sid);
  };

  const filterPlans = (sid: number) => {
    return plans.value?.filter((item) => item.sectionId === sid);
  };

  const patchHref = (nav: API.Base, item: API.Base, sub: API.Base) => {
    return [nav.url, item.url, sub.id].join('/').replace(/\/+/g, '/');
  };

  const navs = computed(() => {
    return sections.value?.map((item) => ({
      ...item,
      children: item.children?.map((sub) => ({
        ...sub,
        children: sub.type === 'product'
          ? filterProducts(sub.id)
          : sub.type === 'plan'
            ? filterPlans(sub.id)
            : []
      })),
    }))
  });

  return { navs, patchHref };
}
