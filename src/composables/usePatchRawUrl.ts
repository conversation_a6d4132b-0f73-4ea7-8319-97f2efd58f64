/**
 * 安全地获取 patchRawUrl 函数的 composable
 * 解决 SSR 期间全局变量未初始化的问题
 */
export default function usePatchRawUrl() {
  // 尝试从 Nuxt app 获取
  try {
    const nuxtApp = useNuxtApp();
    if (nuxtApp && nuxtApp.$patchRawUrl) {
      return nuxtApp.$patchRawUrl;
    }
  } catch (error) {
    // 在 SSR 期间可能会失败，继续使用 fallback
  }

  // 尝试从全局变量获取
  if (typeof globalThis !== 'undefined' && globalThis.$patchRawUrl) {
    return globalThis.$patchRawUrl;
  }

  // Fallback 实现
  return function patchRawUrlFallback(uri?: string): string {
    if (!uri) return '';

    // 在 SSR 期间，如果无法获取配置，直接返回原始 URI
    // 这样可以避免在服务器端渲染时出错
    if (process.server) {
      // 在服务器端，我们可以尝试从环境变量获取配置
      const host = process.env.NUXT_RAW_HOST || 'http://localhost:3201';

      if (typeof uri === 'string' && (uri.startsWith('/raw') || uri.startsWith('/images') || uri.startsWith('/video') || uri.startsWith('/file'))) {
        try {
          return new URL(uri, host).toString();
        } catch (error) {
          console.warn('Unable to create URL during SSR:', error);
          return uri;
        }
      }

      return uri;
    }

    // 客户端尝试获取运行时配置
    try {
      const config = useRuntimeConfig();
      const host = config.public.NUXT_RAW_HOST;

      if (typeof uri === 'string' && (uri.startsWith('/raw') || uri.startsWith('/images') || uri.startsWith('/video') || uri.startsWith('/file'))) {
        return new URL(uri, host).toString();
      }
    } catch (error) {
      // 如果无法获取配置，返回原始 URI
      console.warn('Unable to patch raw URL:', error);
    }

    return uri;
  };
}
