import type { MaybeComputedRef, MaybeRef } from '@vueuse/core';

export type Options = {
  root?: MaybeComputedRef<HTMLElement | Window>;
  offset?: { top?: number; bottom?: number };
};

const isStr = (v: unknown): v is string => typeof v === 'string';
const isEle = (v: unknown): v is Element => v && (v as Element).nodeType === Node.ELEMENT_NODE;

function getTargets(targets: MaybeRef<(HTMLElement | string)[]>) {
  const items = isRef(targets) ? targets.value : targets;

  return items?.map((el) => isStr(el) ? document.querySelector(el) : el) ?? [];
}

function getRootEl(root?: Options['root']) {
  if (!root) return window;

  const el = isRef(root) ? root.value : root;

  if (typeof el === 'function') {
    return el();
  }

  return el;
}

function getRootRect(root: Element | Window) {
  if (isEle(root)) {
    return root.getBoundingClientRect();
  }

  return { top: 0, height: window.innerHeight };
}

export default function useScrollSpy(targets: MaybeRef<(HTMLElement | string)[]>, options?: Options) {
  const index = ref(0);
  const els = ref<Element[]>();
  const root = ref<HTMLElement | Window>();

  const { y, arrivedState, isScrolling, directions } = useScroll(root, {
    offset: options.offset,
  });

  const isInView = (el: Element) => {
    const rRect = getRootRect(root.value);
    const rect = el.getBoundingClientRect();
    const offsetTop = options?.offset?.top ?? 0;
    const offsetBottom = options?.offset?.bottom ?? 0;

    const scrollTop = y.value;
    const top = scrollTop + rect.top - rRect.top - offsetTop;
    const bottom = top + rect.height - offsetBottom;
    const scrollBottom = scrollTop + rRect.height;

    return bottom > scrollTop && top < scrollBottom;
  };

  const onSpy = useDebounceFn(() => {
    if (!els.value || !root.value) return;

    for (let i = 0; i < els.value.length; i++) {
      const el = els.value[i];

      if (!el) continue;

      if (isInView(el)) {
        index.value = i;
        break;
      }
    }
  }, 1000 / 60);

  onMounted(() => {
    els.value = getTargets(targets);
    root.value = getRootEl(options.root);
  });

  watch(y, onSpy);

  return {
    index,
    arrivedState,
    isScrolling,
    directions,
  }
}
