import { useSites } from '~/api/common';

export default function useSiteMeta(title?: string) {
  const { data: site } = useSites();
  const patchRawUrl = usePatchRawUrl();

  const metaObj = reactive({
    title,
    meta: [
      { name: 'keywords', content: site.value?.keywords ?? '' },
      { name: 'description', content: site.value?.description ?? '' },
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: patchRawUrl(site.value?.favicon) }
    ],
    titleTemplate: (a: string) => {
      return [a, site.value?.title].filter(Boolean).join(' - ');
    },
  });

  watch(site, () => {
    if (site.value) {
      const { favicon, keywords, description } = site.value;
      metaObj.meta = [
        { name: 'keywords', content: keywords },
        { name: 'description', content: description },
      ];
      metaObj.link = [
        { rel: 'icon', type: 'image/x-icon', href: patchRawUrl(favicon) }
      ];
    }
  }, { immediate: true });

  useHead(metaObj);
}
