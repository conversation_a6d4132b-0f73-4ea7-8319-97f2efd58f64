import { Ref } from 'vue';
import { Swiper, SwiperOptions } from 'swiper';
import { CSSSelector } from 'swiper/types/shared';

import 'swiper/less';
import 'swiper/less/navigation';
import 'swiper/less/pagination';
import 'swiper/less/effect-coverflow';

export default function useSwiper(el: Ref<HTMLElement> | HTMLElement | CSSSelector, options?: SwiperOptions, refs?:{ [key: string]: any }) {
  const swiper = ref<Swiper>();
  // debugger;
  const initSwiper = () => {
    if(refs){
      if(refs.paginationConfig){
        options.pagination = {
          ...refs.paginationConfig,
          el: refs.paginationConfig.el.value,
        }
      }
      if(refs.navigationConfig){
        options.navigation = {
         ...refs.navigationConfig,
          nextEl: refs.navigationConfig.nextEl.value,
          prevEl: refs.navigationConfig.prevEl.value,
        }
      }
    }
    swiper.value = new Swiper(isRef(el) ? el.value : el, options);
  };

  useDelayMounted(initSwiper);

  return swiper;
}
