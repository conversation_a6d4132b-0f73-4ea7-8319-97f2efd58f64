<!--
  自定义布局
-->
<template>
<div class="layout flex flex-column">
  <!-- <ax-header class="layout-header" /> -->

  <slot class="layout-content" />

  <ax-footer class="layout-footer" />
</div>
</template>

<script lang="ts">
</script>

<style lang="less">
:root {
  --ax-header-height: 130px;
  --ax-header-compact-height: 116px;
}

.layout {
  display: flex;
  min-height: 100vh;

  &-header {
    height: var(--ax-header-height);
  }

  &-content {
    flex: 1 0 auto;
  }

  // &-header + &-content {
  //   padding-top: var(--ax-header-height);
  // }
}
</style>
