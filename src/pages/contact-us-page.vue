<template>
    <div class="news">
        <navbar />
        <PageTopBanner :data="pageTopBannerData"></PageTopBanner>
        <ContactUs></ContactUs>

        <Footer></Footer>

    </div>
</template>

<script setup lang="ts">
const pageTopBannerData = {
    title: '联系我们',
    resource: {
        type: 'image',
        data: {
            url: '/test/pageTop.jpg',
            alt: '图片描述',
        }
    }
};

</script>

<style lang="less" scoped>
.news {
}
@media (max-width: 768px) {
    .news {
    }
}
</style>