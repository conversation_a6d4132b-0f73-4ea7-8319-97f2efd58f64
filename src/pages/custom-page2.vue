<template>
    <!-- <component :is="components[currentComponent]" :data="bannerData1" /> -->
    <div>
        <navbar />
        <template v-for="(item, index) in componentList" :key="index">
            <component :is="item.component" v-bind="item.props" />
        </template>
        {{ componentList }}
        <Footer></Footer>
    </div>
</template>
<script setup>
import { usePageData } from '@/api/page';


const loadComponents = async (id) => {
    const config = useRuntimeConfig();
    const baseURL = config.public.NUXT_API_BASE;

    const { data: componentConfig } = await useAsyncData(
        () => $fetch(baseURL + '/page-manage/id?id=' + id)
    )

    // const { data: componentConfig } =  usePageData(id);


    return componentConfig.value.data.map(item => {
        return {
            name: item.name,
            component: defineAsyncComponent(() => import(`~/components/${item.name}.vue`)),
            props: item.props
        }
    });
}

const componentList = await loadComponents('6868dcd30c9b336d4f17c36d');


// loadComponents();
</script>
