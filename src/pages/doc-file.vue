<template>
    <div class="doc-file">
        <navbar />
        <PageTopBanner :data="pageTopBannerData"></PageTopBanner>
        <FileDownload :data="fileDownloadData"></FileDownload>
        <Footer></Footer>

    </div>
</template>

<script setup lang="ts">
import FileDownload from '../components/FileDownload.vue';

const pageTopBannerData = {
    title: '欢迎来到松灵机器人文档中心',
    titleLeft: 'true',
    resource: {
        type: 'image',
        data: {
            url: '/test/pageTop.jpg',
            alt: '图片描述',
        }
    }
};

const fileDownloadData = {
    title: '产品模型/单页下载',
    privacyFile: '/test/privacy.pdf', // 隐私政策文件
    usePolicy: '/test/terms.pdf', // 使用条款文件
    productList: [
        {
            name: '移动底盘',
            subTypes: [
                {
                    name: '轻量系列',
                    options: [
                        { name: 'SCOUT MINI', checked: false },
                        { name: 'SCOUT 2.0', checked: false },
                        { name: 'HUNTER SE', checked: false }
                    ]
                },
                {
                    name: '重载系列',
                    options: [
                        { name: 'HUNTER 2.0', checked: false },
                        { name: 'TRACER', checked: false }
                    ]
                }
            ]
        },
        {
            name: '机械臂',
            subTypes: [
                {
                    name: '协作机械臂',
                    options: [
                        { name: 'Z1 机械臂', checked: false },
                        { name: 'X1 机械臂', checked: false }
                    ]
                }
            ]
        }
    ],
    list: [
        {
            name: '移动底盘系列',
            files: [
                {
                    name: 'SCOUT MINI 产品单页',
                    url: '/test/pageTop.jpg',
                    type: 'file',
                },
                {
                    name: 'SCOUT MINI 产品模型',
                    url: '/test/pageTop.jpg',
                    type: 'model',
                }
            ]
        },
        {
            name: '机械臂系列',
            files: [
                {
                    name: '产品模型',
                    url: 'https://www.baidu.com'
                },
                {
                    name: '单页下载',
                    url: 'https://www.baidu.com'
                }
            ]
        },
        {
            name: '教育/套件系列',
            files: [
                {
                    name: '产品模型',
                    url: 'https://www.baidu.com'
                },
                {
                    name: '单页下载',
                    url: 'https://www.baidu.com'
                }
            ]
        },
    ]
}

</script>

<style lang="less" scoped>
.news {}

@media (max-width: 768px) {
    .news {}
}
</style>