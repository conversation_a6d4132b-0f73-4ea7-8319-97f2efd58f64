<template>
    <div>
        <navbar />
        <template v-for="(item, index) in componentList" :key="index">
            <component :is="item.component" v-bind="item.props" />
        </template>
        <Footer></Footer>
    </div>
</template>
<script setup>
const route = useRoute()
const id = route.params.id;
let pageTItle = "";
const loadComponents = async (id) => {
    const config = useRuntimeConfig();
    const baseURL = config.public.NUXT_API_BASE;

    const { data: componentConfig } = await useAsyncData(
        () => $fetch(baseURL + '/components/preview?id=' + id)
    )

    pageTItle = componentConfig.value.data.name;

    return componentConfig.value.data.componentList.filter(item => !item.globalNull).map(item => {
        return {
            name: item.name,
            component: defineAsyncComponent(() => import(`../../components/${item.name}.vue`)),
            props: item.props
        }
    });
}

const componentList = await loadComponents(id);
useSiteMeta(pageTItle);
</script>
