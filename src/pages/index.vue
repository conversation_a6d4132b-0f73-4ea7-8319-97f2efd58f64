<template>
    <div :class="theme">
        <navbar />
        <template v-for="(item, index) in componentList" :key="index">
            <component :is="item.component" v-bind="item.props" />
        </template>
        <Footer></Footer>
    </div>
</template>
<script setup>
import { th } from '@formkit/i18n';

const route = useRoute()
const id = route.params.id;
let pageTItle = "";
const theme = ref("light");
const loadComponents = async (id) => {
    const config = useRuntimeConfig();
    const baseURL = config.public.NUXT_API_BASE;

    const { data: componentConfig } = await useAsyncData(
        () => $fetch(baseURL + '/sites/homepage')
    )

    pageTItle = componentConfig.value.data.name;
    theme.value = componentConfig.value.data.theme == 'dark' ? 'dark-theme' : 'light-theme';
    console.log(componentConfig.value.data)

    return componentConfig.value.data.componentList.filter(item => !item.globalNull).map(item => {
        return {
            name: item.name,
            component: defineAsyncComponent(() => import(`../components/${item.name}.vue`)),
            props: item.props
        }
    });
}

const componentList = await loadComponents(id);
useSiteMeta(pageTItle);
</script>
