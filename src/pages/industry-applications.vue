<template>
    <div class="industry-applications">
        <navbar />
        <banner :data="bannerData1"></banner>

        <big-banner-no-loop-with-pagination :data="bigBannerData2"></big-banner-no-loop-with-pagination>

        <BigBannerWithPagination :data="bigBannerData3"></BigBannerWithPagination>

        <IndustryImgCard :data="IndustryImgCardData"></IndustryImgCard>

        <external-video :data="externalVideosData" />

        <external-video-more-text :data="externalVideosData" />

        <product-select :data="productSelectData" />

        <ContactUs></ContactUs>

        <CoreAdvantages :data="coreAdvantagesData"></CoreAdvantages>

        <IndustryTextCard :data="IndustryTextCardData"></IndustryTextCard>

        <Footer></Footer>

    </div>
</template>

<script setup lang="ts">
import IndustryImgCard from '../components/IndustryImgCard.vue';
import Product from './product.vue';


const bannerData1 = {
    type: 'no-btn',
    list: [
        {
            title: '我是主标题3',
            subTitle: '我是描述内容3我是描述内容3我是描述内容3',
            resource: {
                type: 'image',
                data: {
                    url: '/test/01.JPG',
                    alt: '图片描述',
                }
            }
        }
    ]
};

const bigBannerData2 = {
    title: '核心技术亮点',
    desc: '我是副标题我是副标题我是副标题我是副标题',
    list: [
        {
            name: 'SCOU MINI视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议及CAN总线支持快速二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 663.png'
            }
        },
        {
            name: 'SCOU RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/01.mp4'
            }
        },
        {
            name: 'RANGER图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/02.mp4'
            }
        },
        {
            name: 'RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 680.png'
            }
        },
    ]
}

const bigBannerData3 = {
    title: '应用场景',
    desc: 'NAVIS导航系统广泛应用于工厂、仓库、商场、养老院等多种场景，满足不同行业的自主移动需求',
    list: [
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'SCOU MINI视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议及CAN总线支持快速二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 683.png'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'SCOU RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/01.mp4'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'RANGER图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/02.mp4'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 680.png'
            }
        },
    ]
}

const IndustryImgCardData = {
    title: '应用领域',
    subTitle: '我是副标题我是副标题我是副标题我是副标题我是副标题我是副标题',
    list: [
        {
            title: '智能导航演示',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp1.jpg',
            url: '//player.bilibili.com/player.html?isOutside=true&aid=537486828&bvid=BV1xi4y1a7kc&cid=1371365241&p=1'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp2.jpg',
            url: 'https://player.vimeo.com/video/234567'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp3.jpg',
            url: 'https://player.vimeo.com/video/234567'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp4.jpg',
            url: 'https://player.vimeo.com/video/234567'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp4.jpg',
            url: 'https://player.vimeo.com/video/234567'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp4.jpg',
            url: 'https://player.vimeo.com/video/234567'
        }
    ]
};

const externalVideosData = {
    title: '客户案例视频',
    subTitle: '观看我们的产品演示视频',
    videos: [
        {
            title: '智能导航演示',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp1.jpg',
            url: 'http://player.bilibili.com/player.html?isOutside=true&aid=824085746&bvid=BV1Gg4y1s7z8&cid=1068982002&p=1'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp2.jpg',
            url: 'https://player.vimeo.com/video/234567'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp3.jpg',
            url: 'https://player.vimeo.com/video/234567'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp4.jpg',
            url: 'https://player.vimeo.com/video/234567'
        }
    ]
};

const productSelectData = {
    title: '哪款底盘更适合你',
    subTitle: '针对不同农业场景的智能机器人解决方案',
    productList: [
        {
            title: 'SCOU MINI',
            subTitle: '我是副标题我是副标题我是副标题',
            img: '/test/dp1.png',
            specs: [
                {
                    key: '尺寸',
                    value: '100mm*100mm*100mm',
                },
                {
                    key: '轮距',
                    value: '640mm',
                },
                {
                    key: '轴距',
                    value: '494mm'
                },
                {
                    key: '离地间距',
                    value: '105mm'
                },
                {
                    key: '最大负载',
                    value: '200kg'
                },
                {
                    key: '最大速度',
                    value: '1.5m/s'
                },
                {
                    key: '最大加速度',
                    value: '1.5m/s²'
                },
                {
                    key: '最大扭矩',
                    value: '100N*m'
                }
            ]
        },
        {
            title: 'SCOU MINI',
            subTitle: '我是副标题我是副标题我是副标题',
            img: '/test/dp2.png',
            specs: [
                {
                    key: '尺寸',
                    value: '100mm*100mm*100mm',
                },
                {
                    key: '轮距',
                    value: '640mm',
                },
                {
                    key: '轴距',
                    value: '494mm'
                },
                {
                    key: '离地间距',
                    value: '105mm'
                },
                {
                    key: '最大负载',
                    value: '200kg'
                },
                {
                    key: '最大速度',
                    value: '1.5m/s'
                },
                {
                    key: '最大加速度',
                    value: '1.5m/s²'
                },
                {
                    key: '最大扭矩',
                    value: '100N*m'
                }
            ]
        },
        {
            title: 'SCOU MINI',
            subTitle: '我是副标题我是副标题我是副标题',
            img: '/test/dp3.png',
            specs: [
                {
                    key: '尺寸',
                    value: '100mm*100mm*100mm',
                },
                {
                    key: '轮距',
                    value: '640mm',
                },
                {
                    key: '轴距',
                    value: '494mm'
                },
                {
                    key: '离地间距',
                    value: '105mm'
                },
                {
                    key: '最大负载',
                    value: '200kg'
                },
                {
                    key: '最大速度',
                    value: '1.5m/s'
                },
                {
                    key: '最大加速度',
                    value: '1.5m/s²'
                },
                {
                    key: '最大扭矩',
                    value: '100N*m'
                }
            ]
        },
    ]
}

const coreAdvantagesData = {
    title:  '核心优势',
    topCards:[
        {
            title:'专业底盘定制',
            desc:'根据您的载重需求、运行环境和应用场景，量身定制机器人底盘结构、尺寸材质',
            img:'/test/hydz.png',
        },
        {
            title:'专业机器人定制',
            desc:'根据您的载重需求、运行环境和应用场景，量身定制机器人结构、尺寸材质',
            img:'/test/hydz.png',
        },
        {
            title:'专业机器人定制',
            desc:'根据您的载重需求、运行环境和应用场景，量身定制机器人结构、尺寸材质',
            img:'/test/hydz.png',
        }
    ],
    bottomCards:[
        {
            title:'专业经验',
            desc:'拥有多年机器人底盘研发经验，技术团队来自顶尖',
        },
        {
            title:'专业经验',
            desc:'拥有多年机器人底盘研发经验，技术团队来自顶尖',
        },
        {
            title:'专业经验',
            desc:'拥有多年机器人底盘研发经验，技术团队来自顶尖',
        },
        {
            title:'专业经验',
            desc:'拥有多年机器人底盘研发经验，技术团队来自顶尖',
        },
    ]
}

const IndustryTextCardData = {
    title:  '定制化方案系列',
    cards:[
        {
            title:'专业底盘定制',
            desc:'根据您的载重需求、运行环境和应用场景，量身定制机器人底盘结构、尺寸材质',
            img:'/test/hydz.png',
        },
        {
            title:'专业机器人定制',
            desc:'根据您的载重需求、运行环境和应用场景，量身定制机器人结构、尺寸材质',
            img:'/test/hydz.png',
        },
        {
            title:'专业机器人定制',
            desc:'根据您的载重需求、运行环境和应用场景，量身定制机器人结构、尺寸材质',
            img:'/test/hydz.png',
        }
    ]
}




const scenariosData = {
    title: '行业应用场景',
    subTitle: '探索我们的解决方案如何赋能不同领域',
    scenarios: [
        {
            title: '智慧物流',
            description: '全天候自动化仓储巡检与管理',
            imageUrl: '/industry/logistics.jpg'
        },
        {
            title: '能源巡检',
            description: '复杂地形下的电力设施智能巡查',
            imageUrl: '/industry/energy.jpg'
        },
        {
            title: '农业自动化',
            description: '精准农业管理与作物监测',
            imageUrl: '/industry/agriculture.jpg'
        }
    ]
}
</script>

<style lang="less" scoped>
.industry-applications {
    :deep(.application-scenarios) {
        padding-top: 80px;
    }
}
</style>