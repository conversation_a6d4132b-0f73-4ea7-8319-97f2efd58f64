<template>
    <navbar />
    <banner :data="bannerData1"></banner>
    <!-- <banner :bannerData="bannerData2"></banner> -->
    <!-- <banner :bannerData="bannerData3"></banner> -->

    <img-card :data="imgCardData"></img-card>
    <block-height :style="{ height: '200px' }"></block-height>

    <big-banner :data="bigBannerData"></big-banner>

    <block-height :style="{ height: '200px' }"></block-height>
    <banner2 :data="banner2Data"></banner2>

    <block-height :style="{ height: '200px' }"></block-height>
    <img-card2 :data="imgCard2Data"></img-card2>

    <block-height :style="{ height: '200px' }"></block-height>
    <company-partners :data="partnersData"></company-partners>
    <block-height :style="{ height: '200px' }"></block-height>

    <Footer></Footer>

    <i class="up-top-btn" :class="{ show: showBtn }" @click="handleScrollTop"></i>

</template>

<script setup lang='ts'>
import { onMounted, onUnmounted, ref } from 'vue'

const showBtn = ref(false)

const handleScroll = () => {
    showBtn.value = document.documentElement.scrollTop > 1000
}

const handleScrollTop = () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    })
}

onMounted(() => {
    window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
})

const bannerData1 = [
    {
        title: '我是主标题3',
        subTitle: '我是副标题3我是副标题3我是副标题3',
        resource: {
            type: 'image',
            data: {
                url: '/test/01.JPG',
                alt: '图片描述',
            }
        }
    },
    {
        title: '我是主标题2',
        subTitle: '我是副标题2我是副标题2我是副标题2',
        resource: {
            type: 'video',
            data: {
                url: '/test/01.mp4',
            }
        }
    },
    {
        title: '我是主标题3',
        subTitle: '我是副标题3我是副标题3我是副标题3',
        resource: {
            type: 'video',
            data: {
                url: '/test/02.mp4',
            }
        }
    },
];

const bannerData2 = [
    {
        title: '我是主标题3',
        subTitle: '我是副标题3我是副标题3我是副标题3',
        resource: {
            type: 'image',
            data: {
                url: '/test/02.JPG',
                alt: '图片描述',
            }
        }
    },
    // {
    //     title: '我是主标题2',
    //     subTitle: '我是副标题2',
    //     resource: {
    //         type: 'video',
    //         data: {
    //             url: '/test/03.mp4',
    //         }
    //     }
    // },
    // {
    //     title: '我是主标题3',
    //     subTitle: '我是副标题3',
    //     resource: {
    //         type: 'video',
    //         data: {
    //             url: '/test/04.mp4',
    //         }
    //     }
    // },
    // {
    //     title: '我是主标题3',
    //     subTitle: '我是副标题3',
    //     resource: {
    //         type: 'image',
    //         data: {
    //             url: '/test/07.JPG',
    //             alt: '图片描述',
    //         }
    //     }
    // },
    // {
    //     title: '我是主标题3',
    //     subTitle: '我是副标题3',
    //     resource: {
    //         type: 'image',
    //         data: {
    //             url: '/test/08.JPG',
    //             alt: '图片描述',
    //         }
    //     }
    // },
];

const bannerData3 = [
    // {
    //     title: '我是主标题3',
    //     subTitle: '我是副标题3',
    //     resource: {
    //         type: 'image',
    //         data: {
    //             url: '/test/03.JPG',
    //             alt: '图片描述',
    //         }
    //     }
    // },
    // {
    //     title: '我是主标题3',
    //     subTitle: '我是副标题3',
    //     resource: {
    //         type: 'image',
    //         data: {
    //             url: '/test/05.JPG',
    //             alt: '图片描述',
    //         }
    //     }
    // },
    // {
    //     title: '我是主标题2',
    //     subTitle: '我是副标题2',
    //     resource: {
    //         type: 'video',
    //         data: {
    //             url: '/test/04.mp4',
    //         }
    //     }
    // },
    // {
    //     title: '我是主标题2',
    //     subTitle: '我是副标题2',
    //     resource: {
    //         type: 'video',
    //         data: {
    //             url: '/test/01.mp4',
    //         }
    //     }
    // },
    {
        title: '我是主标题3',
        subTitle: '我是副标题3我是副标题3我是副标题3',
        resource: {
            type: 'video',
            data: {
                url: '/test/03.mp4',
            }
        }
    },
    // {
    //     title: '我是主标题3',
    //     subTitle: '我是副标题3',
    //     resource: {
    //         type: 'image',
    //         data: {
    //             url: '/test/06.JPG',
    //             alt: '图片描述',
    //         }
    //     }
    // },
];

const imgCardData = [
    {
        title: '我是主标题',
        subTitle: '我是副标题我是副标题我是副标题',
        img: '/test/Rectangle 679.png'
    },
    {
        title: '我是主标题',
        subTitle: '我是副标题我是副标题我是副标题',
        img: '/test/Rectangle 680.png'
    },
    {
        title: '我是主标题',
        subTitle: '我是副标题我是副标题我是副标题',
        img: '/test/Rectangle 681.png'
    },
    {
        title: '我是主标题',
        subTitle: '我是副标题我是副标题我是副标题',
        img: '/test/Rectangle 682.png'
    },
]


const bigBannerData = {
    title: '基于全新能耗设计，专为行业应用而生',
    desc: '机身紧凑，能耗低，搭载全新散热系统及安全防护系统，室内户外来去自如，四轮驱动，差速自转，动力充沛，轻松驾驭复杂环境，标准协议及CAN总线支持快速二次开发，为您呈现最佳全能型行业移动平台解决方案',
    list: [
        {
            name: 'SCOU MINI视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议及CAN总线支持快速二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'image',
                src: '/test/Rectangle 683.png'
            }
        },
        {
            name: 'SCOU RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                src: '/test/01.mp4'
            }
        },
        {
            name: 'RANGER图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                src: '/test/02.mp4'
            }
        },
        {
            name: 'RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协能型行业移动平台解决方案',
            resource: {
                type: 'image',
                src: '/test/Rectangle 680.png'
            }
        },
    ]
}

const banner2Data = {
    title: '解决方案',
    subTitle: '我是副标题我是副标题我是副标题',
    resource: {
        type: 'video',
        data: {
            url: '/test/03.mp4',
        }
    }
}

const imgCard2Data = {
    title: '优秀案例',
    subTitle: '我是副标题我是副标题我是副标题',
    list: [
        {
            title: 'UMR行业应用',
            subTitle: '我是副标题我是副标题我是副标题',
            img: '/test/Rectangle 692.png'
        },
        {
            title: 'UMR行业应用',
            subTitle: '我是副标题我是副标题我是副标题',
            img: '/test/Rectangle 693.png'
        },
        {
            title: 'UMR行业应用',
            subTitle: '我是副标题我是副标题我是副标题',
            img: '/test/Rectangle 694.png'
        },
        {
            title: 'UMR行业应用',
            subTitle: '我是副标题我是副标题我是副标题',
            img: '/test/Rectangle 695.png'
        },
        {
            title: 'UMR行业应用',
            subTitle: '我是副标题我是副标题我是副标题',
            img: '/test/Rectangle 696.jpg'
        },
    ]
}


const partnersData = {
    title: '优秀案例',
    subTitle: '我是副标题我是副标题我是副标题',
    list: [
        {
            img: '/test/logo1.png'
        },
        {
            img: '/test/logo2.png'
        },
        {
            img: '/test/logo3.png'
        },
        {
            img: '/test/logo1.png'
        },
        {
            img: '/test/logo2.png'
        },
        {
            img: '/test/logo3.png'
        },
        {
            img: '/test/logo1.png'
        },
        {
            img: '/test/logo2.png'
        },
        {
            img: '/test/logo3.png'
        },
    ]
}
</script>

<style lang="less">
.up-top-btn {
    display: block;
    width: 56px;
    height: 56px;
    background: url(../assets/imgs/up_top_icon.png) no-repeat center center, rgba(255, 255, 255, .8);
    background-size: 20px 20px;
    border-radius: 50%;
    position: fixed;
    bottom: 120px;
    right: 40px;
    cursor: pointer;
    z-index: 999;
    transition: all .3s ease-in-out;

    visibility: hidden;
    opacity: 0;
    transform: translateY(20px);

    &.show {
        visibility: visible;
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
