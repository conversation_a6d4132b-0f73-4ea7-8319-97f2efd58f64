<template>
    <div class="navis-applications">
        <navbar />

        <banner :data="bannerData1"></banner>

        <core-tech-highlights :data="coreTechHighlightsData"></core-tech-highlights>

        <metric-display-group :data="metricDisplayData"></metric-display-group>

        <api :data="apiData"></api>

        <BigBannerWithPagination :data="bigBannerData3"></BigBannerWithPagination>

        <external-video-more-text :data="externalVideosData" />

        <service-support :data="serviceSupportData"></service-support>

        <developer-resources :data="developerResourcesData"></developer-resources>

        <ContactUs></ContactUs>

        <CardStack :data="cardStackData"></CardStack>

        <Footer></Footer>

    </div>
</template>

<script setup lang="ts">
import Product from './product.vue';


const bannerData1 = {
    type: 'no-btn',
    list: [
        {
            title: 'NAVIS导航系统banner图',
            subTitle: ' NAVIS导航系统是一套面向移动机器人的高精度、高可靠性导航解决方案，支持多场景适配，提供开放式API接口，助力机器人快速实现自主导航能力',
            resource: {
                type: 'image',
                data: {
                    url: '/test/01.JPG',
                    alt: '图片描述',
                }
            }
        }
    ]
};

const coreTechHighlightsData = {
    title: '核心优势',
    subTitle: 'NAVIS导航系统采用多传感器融合技术，结合深度学习算法，为移动机器人提供高精度、可靠性的导航能力',
    list: [
        {
            name: 'SLAM定位技术',
            title: 'SLAM定位技术',
            desc: 'SLAM定位技术采用多传感器融合技术，结合深度学习算法，为移动机器人提供高精度、可靠性的导航能力',
            img: '/test/core_tab_content.jpg',
            feature: [
                {
                    name: '厘米级定位精度',
                    desc: '在复杂环境中实现±2cm的定位精度，满足高精度导航需求',
                    icon: '/test/icon_location.svg'
                },
                {
                    name: '50Hz的定位频率',
                    desc: '50Hz的定位频率，满足高精度导航需求',
                    icon: '/test/icon_location.svg'

                },
                {
                    name: '99.9%的导航成功率',
                    desc: '99.9%的导航成功率，满足高精度导航需求'
                },
            ]
        },
        {
            name: '智能路径规划',
            title: 'SLAM定位技术',
            desc: 'SLAM定位技术采用多传感器融合技术，结合深度学习算法，为移动机器人提供高精度、可靠性的导航能力',
            img: '/test/core_tab_content.jpg',
            feature: [
                {
                    name: '厘米级定位精度',
                    desc: '在复杂环境中实现±2cm的定位精度，满足高精度导航需求',
                    icon: '/test/icon_location.svg'
                },
                {
                    name: '50Hz的定位频率',
                    desc: '50Hz的定位频率，满足高精度导航需求',
                    icon: '/test/icon_location.svg'
                },
                {
                    name: '99.9%的导航成功率',
                    desc: '99.9%的导航成功率，满足高精度导航需求',
                    icon: '/test/icon_location.svg'
                },
            ]
        },
        {
            name: '多传感器融合',
            title: 'SLAM定位技术',
            desc: 'SLAM定位技术采用多传感器融合技术，结合深度学习算法，为移动机器人提供高精度、可靠性的导航能力',
            img: '/test/core_tab_content.jpg',
            feature: [
                {
                    name: '厘米级定位精度',
                    desc: '在复杂环境中实现±2cm的定位精度，满足高精度导航需求',
                    icon: '/test/icon_location.svg'
                },
                {
                    name: '50Hz的定位频率',
                    desc: '50Hz的定位频率，满足高精度导航需求'
                },
                {
                    name: '99.9%的导航成功率',
                    desc: '99.9%的导航成功率，满足高精度导航需求',
                    icon: '/test/icon_location.svg'
                },
            ]
        }
    ],
}

const metricDisplayData = {
    title: '卓越性能指标',
    subTitle: 'NAVIS导航系统通过严格测试，在各项关键性能指标上表现卓越',
    list: [
        {
            key: '定位精度',
            value: '±2cm'
        },
        {
            key: '定位频率',
            value: '50Hz'
        },
        {
            key: '导航成功率',
            value: '99.9%'
        },
        {
            key: '规划响应时间',
            value: '30ms'
        }
    ]
};

const apiData = {
    title: '开放式API接口',
    subTitle: 'NAVIS导航系统提供全面的API接口，便于快速集成与二次开发',
    desc: [
        '完整的接口文档',
        '多语言SDK支持',
        '示例代码与教程',
        '开发者支持',
        '跨平台兼容',
        '云端模拟接口'
    ],
    list: [
        {
            name: '地图构建',
            content: {
                title: '地图构建API',
                code: `// 创建新地图
POST /api/v1/maps
Content-Type: application/json

{
  "name": "工厂一楼",
  "description": "工厂一楼自动化生产区域",
  "resolution": 0.05
}

// 开始地图构建
POST /api/v1/maps/{map_id}/build
Content-Type: application/jsonapplication/jsonapplication/ jsonapplication/jsonapplication /jsonapplication/jsonapplication/jsonapplication/jsonapplication/json

{
  "mode": "auto",
  "robot_id": "robot_001"
}

// 获取地图构建状态
GET /api/v1/maps/{map_id}/build/status

// 完成并保存地图
POST /api/v1/maps/{map_id}/build/finish`,
                subTitle: 'SLAM定位技术采用多传感器融合技术，结合深度学习算法，为移动机器人提供高精度、可靠性的导航能力',
                desc: `·创建新地图冰设置基本参数
·开始构建地图，并保存地图
·实时获取地图构建进度和状态
·编辑地图，添加虚拟墙、进行曲等
·导出/导入地图数据`
            }
        },
        {
            name: '定位服务',
            content: {
                title: '定位服务API',
                code: `// 创建新地图
POST /api/v1/maps
Content-Type: application/json

{
  "name": "工厂一楼",
  "description": "工厂一楼自动化生产区域",
  "resolution": 0.05
}

// 开始地图构建
POST /api/v1/maps/{map_id}/build
Content-Type: application/jsonapplication/jsonapplication/ jsonapplication/jsonapplication /jsonapplication/jsonapplication/jsonapplication/jsonapplication/json

{
  "mode": "auto",
  "robot_id": "robot_001"
}

// 获取地图构建状态
GET /api/v1/maps/{map_id}/build/status

// 完成并保存地图
POST /api/v1/maps/{map_id}/build/finish`,
                subTitle: 'SLAM定位技术采用多传感器融合技术，结合深度学习算法，为移动机器人提供高精度、可靠性的导航能力',
                desc: `·创建新地图冰设置基本参数
·开始构建地图，并保存地图
·实时获取地图构建进度和状态
·编辑地图，添加虚拟墙、进行曲等
·导出/导入地图数据`
            }
        },
        {
            name: '路径规划',
            content: {
                title: '路径规划API',
                code: `// 创建新地图
POST /api/v1/maps
Content-Type: application/json

{
  "name": "工厂一楼",
  "description": "工厂一楼自动化生产区域",
  "resolution": 0.05
}

// 开始地图构建
POST /api/v1/maps/{map_id}/build
Content-Type: application/jsonapplication/jsonapplication/ jsonapplication/jsonapplication /jsonapplication/jsonapplication/jsonapplication/jsonapplication/json

{
  "mode": "auto",
  "robot_id": "robot_001"
}

// 获取地图构建状态
GET /api/v1/maps/{map_id}/build/status

// 完成并保存地图
POST /api/v1/maps/{map_id}/build/finish`,
                subTitle: 'SLAM定位技术采用多传感器融合技术，结合深度学习算法，为移动机器人提供高精度、可靠性的导航能力',
                desc: `·创建新地图冰设置基本参数
·开始构建地图，并保存地图
·实时获取地图构建进度和状态
·编辑地图，添加虚拟墙、进行曲等
·导出/导入地图数据`
            }
        },
        {
            name: '任务管理',
            content: {
                title: '任务管理API',
                code: `// 创建新地图
POST /api/v1/maps
Content-Type: application/json

{
  "name": "工厂一楼",
  "description": "工厂一楼自动化生产区域",
  "resolution": 0.05
}

// 开始地图构建
POST /api/v1/maps/{map_id}/build
Content-Type: application/jsonapplication/jsonapplication/ jsonapplication/jsonapplication /jsonapplication/jsonapplication/jsonapplication/jsonapplication/json

{
  "mode": "auto",
  "robot_id": "robot_001"
}

// 获取地图构建状态
GET /api/v1/maps/{map_id}/build/status

// 完成并保存地图
POST /api/v1/maps/{map_id}/build/finish`,
                subTitle: 'SLAM定位技术采用多传感器融合技术，结合深度学习算法，为移动机器人提供高精度、可靠性的导航能力',
                desc: `·创建新地图冰设置基本参数
·开始构建地图，并保存地图
·实时获取地图构建进度和状态
·编辑地图，添加虚拟墙、进行曲等
·导出/导入地图数据`
            }
        },
    ],
}

const bigBannerData3 = {
    title: '应用场景',
    desc: 'NAVIS导航系统广泛应用于工厂、仓库、商场、养老院等多种场景，满足不同行业的自主移动需求',
    list: [
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'SCOU MINI视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议及CAN总线支持快速二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 683.png'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'SCOU RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/01.mp4'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'RANGER图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/02.mp4'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 680.png'
            }
        },
    ]
}

const externalVideosData = {
    title: '客户案例视频',
    subTitle: '观看我们的产品演示视频',
    videos: [
        {
            title: '智能导航演示',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp1.jpg',
            url: '//player.bilibili.com/player.html?isOutside=true&aid=537486828&bvid=BV1xi4y1a7kc&cid=1371365241&p=1'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp2.jpg',
            url: 'https://player.vimeo.com/video/234567'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp3.jpg',
            url: 'https://player.vimeo.com/video/234567'
        },
        {
            title: '自动避障测试',
            desc: '采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为采用NAVIS导航系统为',
            percentData: [
                {
                    key: '效率提升',
                    value: '100%',
                },
                {
                    key: '成本降低',
                    value: '80%',
                },
                {
                    key: '工作效率',
                    value: '95%',
                }
            ],
            thumbnail: '/test/vp4.jpg',
            url: 'https://player.vimeo.com/video/234567'
        }
    ]
};

const serviceSupportData = {
    title: '服务支持',
    subTitle: '我们提供专业的服务支持，帮助用户解决各种问题。',
    list: [
        {
            title: '系统部署',
            desc: '专业团队提供现场部署服务，包含环境评估、系统安装、地图构建、参数调优等，确保系统快速上线',
            icon: '/test/server_support1.svg'
        },
        {
            title: '培训服务',
            desc: '专业团队提供现场部署服务，包含环境评估、系统安装、地图构建、参数调优等，确保系统快速上线',
            icon: '/test/server_support2.svg'
        },
        {
            title: '技术支持',
            desc: '专业团队提供现场部署服务，包含环境评估、系统安装、地图构建、参数调优等，确保系统快速上线',
            icon: '/test/server_support1.svg'
        },
        {
            title: '系统升级',
            desc: '专业团队提供现场部署服务，包含环境评估、系统安装、地图构建、参数调优等，确保系统快速上线',
            icon: '/test/server_support2.svg'
        }
    ]
}

const developerResourcesData = {
    title: "开发者资源",
    list: [
        {
            title: '示例代码',
            desc: '多语言SDK和示例程序',
            icon: '/test/ss1.svg'
        },
        {
            title: '技术文档',
            desc: '详细的API文档和使用指南',
            icon: '/test/ss2.svg'
        },
        {
            title: '视频教程',
            desc: '系统使用和开发教程',
            icon: '/test/ss3.svg'
        }
    ]
};

const cardStackData = {
    title: "客户案例",
    subTitle: "我是副标题我是副标题我是副标题我是副标题",
    cards: [
        {
            title: '（澳大利亚）矿场巡检项目',
            img: '/test/vp1.jpg',
        },
        {
            title: '（澳大利亚）矿场巡检项目',
            img: '/test/vp2.jpg',
        },
        {
            title: '（澳大利亚）矿场巡检项目',
            img: '/test/vp3.jpg',
        }
    ]
}
</script>

<style lang="less" scoped>
.navis-applications {
    :deep(.application-scenarios) {
        padding-top: 80px;
    }
}
</style>