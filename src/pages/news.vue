<template>
    <div class="news">
        <navbar />
        <page-top-banner :data="pageTopBannerData"></page-top-banner>
        <news-list></news-list>

        <Footer></Footer>

    </div>
</template>

<script setup lang="ts">
const pageTopBannerData = {
    title: '赛事/新闻',
    titleLeft:'true',
    resource: {
        type: 'image',
        data: {
            url: '/test/pageTop.jpg',
            alt: '图片描述',
        }
    }
};

const newsListData = {
    topImg:'/test/pageTop.jpg',
    type: [
        {
            name:'产品新闻',
            list:[
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
            ]
        },
        {
            name:'赛事活动',
            list:[
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
            ]
        },
        {
            name:'展会资讯',
            list:[
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
                {
                    title:'我是主标题',
                    subTitle:'我是副标题我是副标题我是副标题',
                    img: '/test/news.jpg',
                    time: '2021-09-01',
                },
            ]
        }
    ]
}

</script>

<style lang="less" scoped>
.news {
}
@media (max-width: 768px) {
    .news {
    }
}
</style>