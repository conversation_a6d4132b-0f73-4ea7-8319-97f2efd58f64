<template>
    <div class="main-container" :class="{ 'dark-theme': isDarkTheme }" :style="{ backgroundColor: themeColor }">
        <navbar />
        <banner :data="bannerData1"></banner>

        <product-img-card :data="productImgCardData"></product-img-card>

        <big-banner-sticky-scale :data="bigBannerData2"></big-banner-sticky-scale>

        <three-d-viewer></three-d-viewer>

        <center-banner :data="banner2Data"></center-banner>

        <application-scenarios :data="applicationScenariosData"></application-scenarios>

        <BigBannerWithPagination :data="bigBannerData3"></BigBannerWithPagination>

        <ItemComparison :data="comparisonData"></ItemComparison>

        <product-specs :data="specsData"></product-specs>

        <Footer></Footer>

        <i class="up-top-btn" :class="{ show: showBtn }" @click="handleScrollTop"></i>
    </div>

</template>

<script setup lang='ts'>
import { onMounted, onUnmounted, ref } from 'vue'
import ProductImgCard from '../components/ProductImgCard.vue'

const showBtn = ref(false)

//主题色
const themeColor = ref('#fff');
// const themeColor = ref('');
const isDarkTheme = ref(false);

const handleScroll = () => {
    showBtn.value = document.documentElement.scrollTop > 1000
}

const handleScrollTop = () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    })
}

onMounted(() => {
    window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
})

const bannerData1 = {
    type: 'swiper',
    list: [
        {
            title: '我是主标题3',
            subTitle: '我是描述内容3我是描述内容3我是描述内容3',
            resource: {
                type: 'image',
                data: {
                    url: '/test/01.JPG',
                    alt: '图片描述',
                }
            }
        },
        {
            title: '我是主标题3',
            subTitle: '我是描述内容3我是描述内容3我是描述内容3',
            resource: {
                type: 'image',
                data: {
                    url: '/test/01.JPG',
                    alt: '图片描述',
                }
            }
        },
        {
            title: '我是主标题3',
            subTitle: '我是描述内容3我是描述内容3我是描述内容3',
            resource: {
                type: 'image',
                data: {
                    url: '/test/01.JPG',
                    alt: '图片描述',
                }
            }
        },
        {
            title: '我是主标题2',
            subTitle: '我是副标题2我是副标题2我是副标题2',
            resource: {
                type: 'video',
                data: {
                    url: '/test/01.mp4',
                }
            }
        },
        {
            title: '我是主标题3',
            subTitle: '我是副标题3我是副标题3我是副标题3',
            resource: {
                type: 'video',
                data: {
                    url: '/test/02.mp4',
                }
            }
        },
    ]
};
const applicationScenariosData = {
    title: '应用场景',
    subTitle: '探索产品在不同领域的创新应用',
    scenarios: [
        {
            title: '工业自动化',
            description: '实现产线智能化升级，提升生产效率30%以上',
            imageUrl: '/test/yycj1.jpg'
        },
        {
            title: '智慧物流',
            description: '仓储机器人精准导航，分拣准确率达99.9%',
            imageUrl: '/test/yycj2.jpg'
        },
        {
            title: '工业自动化',
            description: '实现产线智能化升级，提升生产效率30%以上',
            imageUrl: '/test/yycj3.jpg'
        },
        {
            title: '智慧物流',
            description: '仓储机器人精准导航，分拣准确率达99.9%',
            imageUrl: '/test/yycj4.jpg'
        },
    ]
};

const productImgCardData = {
    title: '产品亮点',
    subTitle: '我是副标题我是副标题我是副标题我是副标题我是副标题',
    type: 'swiper',
    list: [
        {
            title: '我是主标题1',
            subTitle: '我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1我是描述内容1',
            resource: {
                type: 'video',
                data: {
                    url: '/test/01.mp4',
                }
            }
        },
        {
            title: '我是主标题2',
            subTitle: '我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2我是描述内容2',
            resource: {
                type: 'image',
                data: {
                    url: '/test/01.JPG',
                    alt: '图片描述',
                }
            }
        },
        {
            title: '我是主标题3',
            subTitle: '我是描述内容3我是描述内容3我是描述内容3我是描述内容3我是描述内容3我是描述内容3我是描述内容3我是描述内容3我是描述内容3我是描述内容3我是描述内容3我是描述内容3',
            resource: {
                type: 'image',
                data: {
                    url: '/test/01.JPG',
                    alt: '图片描述',
                }
            }
        },
        {
            title: '我是主标题4',
            subTitle: '我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4我是描述内容4',
            resource: {
                type: 'video',
                data: {
                    url: '/test/01.mp4',
                }
            }
        },
        {
            title: '我是主标题5',
            subTitle: '我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5我是描述内容5',
            resource: {
                type: 'video',
                data: {
                    url: '/test/02.mp4',
                }
            }
        },
    ]
};
const banner2Data = {
    title: '产品视频',
    subTitle: '我是副标题我是副标题我是副标题',
    resource: {
        type: 'video',
        data: {
            url: '/test/03.mp4',
        }
    }
}
const comparisonData = {
    title: '同款车型对比',
    subTitle: '我是副标题我是副标题我是副标题',
    imgs: [
        {
            name: 'Scout mini',
            url: '/test/04.JPG',
        },
        {
            name: 'Scout 2.0',
            url: '/test/06.JPG',
        }
    ]
}
const specsData = {
    list: [
        {
            productName: 'Scout Pro 全地形机器人',
            specs: [
                {
                    type: '动力系统',
                    items: [
                        { key: '电池容量', value: '48V/100Ah 锂电池' },
                        { key: '续航时间', value: '8小时（空载）' },
                        { key: '充电时间', value: '3小时（快充）' },
                        { key: '驱动方式', value: '四轮独立驱动' },
                        { key: '最大速度', value: '8km/h' },
                        { key: '爬坡角度', value: '35°' }
                    ]
                },
                {
                    type: '尺寸',
                    items: [
                        { key: '尺寸', value: '800mm×600mm×450mm' },
                        { key: '重量', value: '55kg' },
                        { key: '最大负载', value: '150kg' },
                        { key: '最小离地间隙', value: '120mm' },
                        { type: 'image', url: '/test/product_car1.jpg', }
                    ]

                },
                {
                    type: '传感器配置',
                    items: [
                        { key: '激光雷达', value: '16线，探测距离200m' },
                        { key: '摄像头', value: '4K 全景摄像头×4' },
                        { key: '超声波传感器', value: '12个，探测距离0.2-5m' },
                        { key: 'IMU', value: '9轴惯性测量单元' },
                        { key: 'GPS', value: '双频RTK定位（精度±1cm）' },
                        { key: '避障雷达', value: '毫米波雷达×2，探测距离100m' }
                    ]
                }
            ]
        },
        {
            productName: 'Scout Mini',
            specs: [
                {
                    type: '动力系统',
                    items: [
                        { key: '电池容量', value: '48V/100Ah 锂电池' },
                        { key: '续航时间', value: '8小时（空载）' },
                        { key: '充电时间', value: '3小时（快充）' },
                        { key: '驱动方式', value: '四轮独立驱动' },
                        { key: '最大速度', value: '8km/h' },
                        { key: '爬坡角度', value: '35°' }
                    ]
                },
                {
                    type: '尺寸',
                    items: [
                        { key: '尺寸', value: '800mm×600mm×450mm' },
                        { key: '重量', value: '55kg' },
                        { key: '最大负载', value: '150kg' },
                        { key: '最小离地间隙', value: '120mm' },
                        { type: 'image', url: '/test/product_car1.jpg', width: 300, height: 162 }
                    ]

                },
                {
                    type: '传感器配置',
                    items: [
                        { key: '激光雷达', value: '16线，探测距离200m' },
                        { key: '摄像头', value: '4K 全景摄像头×4' },
                        { key: '超声波传感器', value: '12个，探测距离0.2-5m' },
                        { key: 'IMU', value: '9轴惯性测量单元' },
                        { key: 'GPS', value: '双频RTK定位（精度±1cm）' },
                        { key: '避障雷达', value: '毫米波雷达×2，探测距离100m' }
                    ]
                }
            ]
        }
    ]
}
const bigBannerData2 = {
    title: '产品亮点',
    desc: '我是副标题我是副标题我是副标题我是副标题',
    list: [
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'SCOU MINI视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议及CAN总线支持快速二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 683.png'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'SCOU RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/01.mp4'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'RANGER图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/02.mp4'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 680.png'
            }
        },
    ]
}
const bigBannerData3 = {
    title: '应用场景',
    desc: 'NAVIS导航系统广泛应用于工厂、仓库、商场、养老院等多种场景，满足不同行业的自主移动需求',
    list: [
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'SCOU MINI视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议及CAN总线支持快速二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 683.png'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'SCOU RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭二次开发，为您呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/01.mp4'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'RANGER图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协议呈现最佳全能型行业移动平台解决方案',
            resource: {
                type: 'video',
                url: '/test/02.mp4'
            }
        },
        {
            title: '我是描述内容3我是描述内容3我是描述内容',
            name: 'RANGER视频/图片',
            subDesc: '动力充沛，轻松驾驭复杂环境，标准协能型行业移动平台解决方案',
            resource: {
                type: 'image',
                url: '/test/Rectangle 680.png'
            }
        },
    ]
}
</script>

<style scoped lang="less">
.up-top-btn {
    display: block;
    width: 56px;
    height: 56px;
    background: url(../assets/imgs/up_top_icon.png) no-repeat center center, rgba(255, 255, 255, .8);
    background-size: 20px 20px;
    border-radius: 50%;
    position: fixed;
    bottom: 120px;
    right: 40px;
    cursor: pointer;
    z-index: 999;
    transition: all .3s ease-in-out;

    visibility: hidden;
    opacity: 0;
    transform: translateY(20px);

    &.show {
        visibility: visible;
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
