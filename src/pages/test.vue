<template>
    <div class="top">我是顶部内容</div>
    <div class="animation-container">
        <div ref="aniCard" class="animation">
            <div class="animation-card" :style="{
                width: `${frameWidth}px`,
                height: `${frameHeight}px`,
                backgroundSize: `${imgWidth}px ${imgHeight}px`,
                backgroundPosition: `${currentPositionX}px ${currentPositionY}px`
            }"></div>
        </div>
    </div>
    <div class="bottom">我是底部内容</div>
</template>
<script setup lang='ts'>

const aniCard = ref<HTMLElement>();
const spriteUrl = '/test/pig.png';
const imgWidth = 1304;
const imgHeight = 641;
const frameCount = 26;
const frameVerticalCount = 4;
const frameHerizontalCount = 8;
const frameWidth = imgWidth / frameHerizontalCount;
const frameHeight = imgHeight / frameVerticalCount;
const scrollSensitivity = 30;
let currentFrame = 0;

const scrollY = ref(0)
const lastScrollY = ref(0)
const isVisible = ref(false)
const rafId = ref(null)

const currentPositionX = computed(() => {
    console.log(-Math.floor(scrollY.value / scrollSensitivity % frameHerizontalCount))
    return -Math.floor(scrollY.value / scrollSensitivity % frameHerizontalCount) * frameWidth
})
const currentPositionY = computed(() => {
    console.log(-Math.floor(scrollY.value / scrollSensitivity / frameHerizontalCount))
    return -Math.floor(scrollY.value / scrollSensitivity / frameHerizontalCount) * frameHeight
})


const handleScroll = () => {
      if (!isVisible.value) return

    // 使用节流优化
    if (!rafId.value) {
        rafId.value = requestAnimationFrame(() => {
            const currentY = window.scrollY
            scrollY.value += currentY - lastScrollY.value
            console.log(scrollY.value)
            lastScrollY.value = currentY
            rafId.value = null
        })
    }
}

// 可见性检测
// const observer = new IntersectionObserver((entries) => {
//   isVisible.value = entries[0].isIntersecting
// })

// console.log(IntersectionObserver)

// 生命周期
onMounted(() => {
    window.addEventListener('scroll', handleScroll, { passive: true })
    if (process.client) {
        console.log('sdfsa')
        const observer = new IntersectionObserver((entries) => {
            console.log(entries)
            isVisible.value = entries[0].isIntersecting
            const currentY = window.scrollY
            lastScrollY.value = currentY;
        },{ threshold: 1})
        console.log(aniCard.value);
        observer.observe(aniCard.value)
    }
})

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
    //   observer.disconnect()
    if (rafId.value) cancelAnimationFrame(rafId.value)
})
</script>
<style lang="less" scoped>
.top {
    width: 100%;
    height: 130vh;
    background-color: #755b5b;
    color: #fff;
    font-size: 20px;
}

.animation-container {
    width: 100%;
    height: 360vh;
    background-color: #fff;

    .animation {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 60vh;
        background-color: aqua;
        position: sticky;
        top: 30vh;
        left: 0;
        overflow: hidden;

        .animation-card {
            width: 163px;
            height: 160px;
            background-image: url(/test/pig.png);
            // background-size: 776 1200;
            /* 横向雪碧图 */
            background-repeat: no-repeat;
            image-rendering: crisp-edges;
            /* 防止缩放模糊 */
            will-change: background-position;
            /* 触发 GPU 加速 */
        }
    }
}

.bottom {
    width: 100%;
    height: 300vh;
    background-color: #755b5b;
}
</style>