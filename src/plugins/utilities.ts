import { unwrap } from '@/utils';
import type { FetchOptions } from 'ohmyfetch';

type Axios = {
  <R = any>(url: string, opts?: FetchOptions): Promise<R | null>;
}

type PatchRawUrl = {
  (uri?: string): undefined | string;
};

declare module '#app' {
  interface NuxtApp {
    $axios: Axios;
    $patchRawUrl: PatchRawUrl;
  }
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $axios: Axios;
    $patchRawUrl: PatchRawUrl;
  }
}

declare global {
  var $axios: Axios;
  var $patchRawUrl: PatchRawUrl;

  namespace NodeJS {
    interface Global {
      $axios: Axios;
      $patchRawUrl: PatchRawUrl;
    }
  }
}

async function axios<R = any>(url: string, opts?: FetchOptions) {
  const config = useRuntimeConfig();
  const baseURL = config.public.NUXT_API_BASE;

  const res = await $fetch<API.BaseRes<R>>(url, { cache: 'no-cache', baseURL, ...opts });

  return unwrap(res);
}

function patchRawUrl(uri: string) {
  if (!uri) return '';

  try {
    const config = useRuntimeConfig();
    const host = config.public.NUXT_RAW_HOST;

    if (typeof uri === 'string' && (uri.startsWith('/raw') || uri.startsWith('/images') || uri.startsWith('/video') || uri.startsWith('/file'))) {
      return new URL(uri, host).toString();
    }

    return uri;
  } catch (error) {
    // 在 SSR 期间如果无法获取配置，使用环境变量作为 fallback
    if (process.server) {
      const host = process.env.NUXT_RAW_HOST || 'http://localhost:3201';

      if (typeof uri === 'string' && (uri.startsWith('/raw') || uri.startsWith('/images') || uri.startsWith('/video') || uri.startsWith('/file'))) {
        try {
          return new URL(uri, host).toString();
        } catch (urlError) {
          console.warn('Unable to create URL during SSR:', urlError);
          return uri;
        }
      }
    }

    // 如果所有方法都失败，返回原始 URI
    console.warn('Unable to patch raw URL:', error);
    return uri;
  }
}

globalThis.$axios = axios;
globalThis.$patchRawUrl = patchRawUrl;

export default defineNuxtPlugin((app) => {
  return {
    provide: { axios, patchRawUrl },
  };
});
