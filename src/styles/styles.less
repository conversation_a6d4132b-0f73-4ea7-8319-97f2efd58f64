/*!
 * 全局通用样式
 */
@import './variables.less';
@import './utilities.less';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  // bootstrap
  --bs-body-bg: #f5f5f7;
  --bs-primary: @primary;
  --bs-link-color: #222;
  --bs-link-hover-color: @primary;
  --bs-primary-rgb: 214, 21, 25;

  // formkit
  --fk-max-width-input: unset;

  // agilex
  --ax-header-height: 65px;
  --ax-header-compact-height: 85px;

  --title-font-weight: 500;
  --sub-title-font-weight: 400;
  --title-theme-color: rgba(0, 0, 0, 0.80);
  --sub-title-theme-bg-color: rgba(0, 0, 0, 0.65);
  --component-theme-bg-color: #fff;
  --component-theme-color: rgba(0, 0, 0, 0.80);
  --component-theme-sub-color: rgba(0, 0, 0, 0.65);


  --component-mb: 200px;
}

.dark-theme {
  --theme-bg-color: #000;
  --title-theme-color: rgba(255, 255, 255, 0.91);
  --sub-title-theme-color: rgba(255, 255, 255, 0.57);
  --component-theme-bg-color: #111;
  --component-theme-color: rgba(255, 255, 255, 1);
  --component-theme-sub-color: rgba(255, 255, 255, 0.57);
  --component-theme-border-color: rgba(255, 255, 255, 0.12);
}

@media (max-width: 768px) {
  :root {
    --component-mb: 40px;
  }
}

@media (min-width: 992px) {
  :root {
    // agilex
    --ax-header-height: 90px;
    --ax-header-compact-height: 110px;
  }
}

body {
  color: @text-color;
  font-weight: 200;
  // min-width: 1024px;
}

a {
  text-decoration: none;
}

pre {
  --bs-font-monospace: inherit;

  font-family: inherit;
  font-size: inherit;
  overflow: unset;
  word-wrap: break-word;
  word-break: break-all;
}

.btn-primary {
  --bs-btn-bg: @primary;
  --bs-btn-border-color: @primary;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: lighten(@primary, 5%);
  --bs-btn-hover-border-color: lighten(@primary, 5%);
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: darken(@primary, 5%);
  --bs-btn-active-border-color: darken(@primary, 5%);
  --bs-btn-disabled-bg: lighten(@primary, 5%);
  --bs-btn-disabled-border-color: lighten(@primary, 5%);
}

.nav-tabs {
  --bs-nav-tabs-link-hover-border-color: unset;
}

.nav-item {
  &::marker {
    content: none;
  }
}

.nav-link,
.btn-primary,
.nav-link.active,
.btn-primary.active {

  &,
  &:hover,
  &:focus {
    isolation: unset !important;
    outline: none !important;
    box-shadow: none !important;
  }
}

.sub-nav {
  --ax-sub-nav-padding-x: 30px;
  --ax-sub-nav-padding-y: 15px;
  display: flex;
  align-items: center;
  padding: var(--ax-sub-nav-padding-y) var(--ax-sub-nav-padding-x);
  background-color: #fff;

  .nav {
    --bs-link-color: #878787;
    --bs-link-space-x: 0;
    --bs-link-hover-color: @primary;
    --bs-link-active-color: @primary;
  }

  .nav-link {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin: 0 var(--bs-link-space-x);

    &.active {
      color: var(--bs-link-active-color);
      font-weight: bold;
    }
  }

  .nav-icon {
    height: 40px;
    margin-bottom: 10px;
  }
}

.nav-icon path {
  stroke: currentColor;
}

.rich-text img {
  max-width: 100%;
}

.page-main-content {
  min-height: 50vh;
}

.card-img-effect {
  overflow: hidden;
  cursor: pointer;

  .card-img-outer {
    position: relative;
    overflow: hidden;
  }

  .card-img,
  .card-img-top,
  .card-img-bottom,
  .card-img-left,
  .card-img-right {
    transition: transform .3s ease;
  }

  &:hover {

    .card-img,
    .card-img-top,
    .card-img-bottom,
    .card-img-left,
    .card-img-right {
      transform: scale(1.2);
    }
  }
}

.pd-1 {
  padding: 0 0 0 3.5%;
}

.pd-2 {
  padding: 0 0 0 10%;
}

.fb-1 {
  font-weight: 100;
}

.fb-2 {
  font-weight: 400;
}

.fb-3 {
  font-weight: 600;
}

.fb-4 {
  font-weight: bold;
}

.fm-1 {
  font-family: Helvetica, Arial, sans-serif;
}

.page-more-index {
  .section-summary {
    font-weight: 100 !important;
  }
}

.about-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 6%;
}

.page-contact {
  .section-form {
    .section-title {
      font-weight: bold;
    }
  }

  .formkit-input {
    border: 1px #f2f2f2 solid !important;
    height: 70px;
    font-size: 18px;
    font-weight: 100;
  }
}

.page-chassis,
.page-education,
.page-solutions {
  .section-dark {
    .section-summary {
      font-weight: 600;
    }
  }

  .chassis-container {
    margin: 0 auto;

    .card-body {
      .card-title span {
        display: inline-block;
      }

      .card-title {
        font-weight: bold;

      }

      .card-title1 {
        font-weight: bold;
        font-size: 30px;
        font-family: Helvetica, Arial, sans-serif;
        color: #000;
      }
    }

    .d-flex {
      text-align: center;

    }

    .btn-primary {
      font-size: 14px;
    }

    .section-summary {
      font-weight: 500;
    }
  }

  .banner {
    .banner-title {
      font-size: 62px;
      font-weight: 600;
    }

    .banner-tex {
      font-size: 26px;
    }

    .btn {
      margin: 40px 0 0 0;
    }
  }
}

@media (min-width: 640px) {
  .chassis-container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .chassis-container {
    max-width: 768px;
  }
}

@media (min-width: 992px) {
  .about-container {
    max-width: 992px;
  }

  .chassis-container {
    max-width: 992px;
  }

  .page-chassis {
    .section-dark {
      .chassis-card {
        .card-title span {
          display: block;
        }
      }
    }
  }

  .sub-nav {
    --ax-sub-nav-padding-x: 100px;

    .nav {
      --bs-link-space-x: 10px;
    }
  }
}

@media (min-width: 1100px) {
  .about-container {
    max-width: 1100px;
    padding: 0 6%;
  }
}

@media (min-width: 1024px) {
  .chassis-container {
    max-width: 1024px;
  }
}

@media (min-width: 1200px) {
  .about-container {
    max-width: 1100px;
    padding: 0 0%;
  }
}

@media (min-width: 1246px) {
  .chassis-container {
    max-width: 1246px;
  }
}

@media (max-width: 991px) {
  .nav-link {

    &,
    &:hover,
    &:focus,
    &:focus-visible {
      isolation: unset;
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
    }
  }

  .sub-nav {
    --ax-sub-nav-padding-x: 20px;
    --ax-sub-nav-padding-y: 10px;

    .nav-link {
      font-size: 14px;
    }

    .nav-icon {
      width: 30px;
      margin-bottom: 0px;
    }

    .nav-item:first-child .nav-icon {
      width: 25px;
    }
  }

  .page-contact {
    .formkit-input {
      height: 50px;
      font-size: 14px;
    }
  }

  .page-chassis,
  .page-education,
  .page-solutions {
    .chassis-container {

      .tab-content #product .col:nth-child(2n),
      .tab-content #technical .col:nth-child(2n) {
        padding-left: 15%;
      }

      .card-body {
        .card-title1 {
          font-size: 16px;
        }
      }
    }

    .banner {
      .text-white {
        padding: 165px 16% 0 6%;
      }

      .btn {
        font-size: 15px;
      }

      .banner-title {
        font-size: 40px;
      }

      .banner-tex {
        font-size: 22px;
      }
    }
  }
}

@media (max-width: 1440px) {
  .chassis-container {
    padding: 0 6%;
  }
}

/* Wobble Horizontal */
@-webkit-keyframes hvr-wobble-horizontal {
  16.65% {
    -webkit-transform: translateX(8px);
    transform: translateX(8px);
  }

  33.3% {
    -webkit-transform: translateX(-6px);
    transform: translateX(-6px);
  }

  49.95% {
    -webkit-transform: translateX(4px);
    transform: translateX(4px);
  }

  66.6% {
    -webkit-transform: translateX(-2px);
    transform: translateX(-2px);
  }

  83.25% {
    -webkit-transform: translateX(1px);
    transform: translateX(1px);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes hvr-wobble-horizontal {
  16.65% {
    -webkit-transform: translateX(8px);
    transform: translateX(8px);
  }

  33.3% {
    -webkit-transform: translateX(-6px);
    transform: translateX(-6px);
  }

  49.95% {
    -webkit-transform: translateX(4px);
    transform: translateX(4px);
  }

  66.6% {
    -webkit-transform: translateX(-2px);
    transform: translateX(-2px);
  }

  83.25% {
    -webkit-transform: translateX(1px);
    transform: translateX(1px);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

.hvr-wobble-horizontal {
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  will-change: transform;
}

.hvr-wobble-horizontal:hover,
.hvr-wobble-horizontal:focus,
.hvr-wobble-horizontal:active {
  -webkit-animation-name: hvr-wobble-horizontal;
  animation-name: hvr-wobble-horizontal;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}


/* Icon Wobble Vertical */
@-webkit-keyframes hvr-icon-wobble-vertical {
  16.65% {
    -webkit-transform: translateY(6px);
    transform: translateY(6px);
  }

  33.3% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px);
  }

  49.95% {
    -webkit-transform: translateY(4px);
    transform: translateY(4px);
  }

  66.6% {
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
  }

  83.25% {
    -webkit-transform: translateY(1px);
    transform: translateY(1px);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes hvr-icon-wobble-vertical {
  16.65% {
    -webkit-transform: translateY(6px);
    transform: translateY(6px);
  }

  33.3% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px);
  }

  49.95% {
    -webkit-transform: translateY(4px);
    transform: translateY(4px);
  }

  66.6% {
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
  }

  83.25% {
    -webkit-transform: translateY(1px);
    transform: translateY(1px);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

.hvr-icon-wobble-vertical {
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  will-change: transform;
}

.hvr-icon-wobble-vertical .hvr-icon {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: transform;
}

.hvr-icon-wobble-vertical:hover .hvr-icon,
.hvr-icon-wobble-vertical:focus .hvr-icon,
.hvr-icon-wobble-vertical:active .hvr-icon {
  -webkit-animation-name: hvr-icon-wobble-vertical;
  animation-name: hvr-icon-wobble-vertical;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

/* Wobble Top */
@-webkit-keyframes hvr-wobble-top {
  16.65% {
    -webkit-transform: skew(-12deg);
    transform: skew(-12deg);
  }

  33.3% {
    -webkit-transform: skew(10deg);
    transform: skew(10deg);
  }

  49.95% {
    -webkit-transform: skew(-6deg);
    transform: skew(-6deg);
  }

  66.6% {
    -webkit-transform: skew(4deg);
    transform: skew(4deg);
  }

  83.25% {
    -webkit-transform: skew(-2deg);
    transform: skew(-2deg);
  }

  100% {
    -webkit-transform: skew(0);
    transform: skew(0);
  }
}

@keyframes hvr-wobble-top {
  16.65% {
    -webkit-transform: skew(-12deg);
    transform: skew(-12deg);
  }

  33.3% {
    -webkit-transform: skew(10deg);
    transform: skew(10deg);
  }

  49.95% {
    -webkit-transform: skew(-6deg);
    transform: skew(-6deg);
  }

  66.6% {
    -webkit-transform: skew(4deg);
    transform: skew(4deg);
  }

  83.25% {
    -webkit-transform: skew(-2deg);
    transform: skew(-2deg);
  }

  100% {
    -webkit-transform: skew(0);
    transform: skew(0);
  }
}

.hvr-wobble-top {
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  will-change: transform;
}

.hvr-wobble-top:hover,
.hvr-wobble-top:focus,
.hvr-wobble-top:active {
  -webkit-animation-name: hvr-wobble-top;
  animation-name: hvr-wobble-top;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

/* Backward */
.hvr-backward {
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -webkit-transition-property: transform;
  transition-property: transform;
  will-change: transform;
}

.hvr-backward:hover,
.hvr-backward:focus,
.hvr-backward:active {
  -webkit-transform: translateX(-8px);
  transform: translateX(-8px);
}

/* Forward */
.hvr-forward {
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -webkit-transition-property: transform;
  transition-property: transform;
  will-change: transform;
}

.hvr-forward:hover,
.hvr-forward:focus,
.hvr-forward:active {
  -webkit-transform: translateX(8px);
  transform: translateX(8px);
}

/* Shadow Radial */
.hvr-shadow-radial {
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  position: relative;
  will-change: transform;
}

.hvr-shadow-radial:before,
.hvr-shadow-radial:after {
  pointer-events: none;
  position: absolute;
  content: '';
  left: 0;
  width: 100%;
  box-sizing: border-box;
  background-repeat: no-repeat;
  height: 5px;
  opacity: 0;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -webkit-transition-property: opacity;
  transition-property: opacity;
}

.hvr-shadow-radial:before {
  bottom: 100%;
  background: -webkit-radial-gradient(50% 150%, ellipse, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 80%);
  background: radial-gradient(ellipse at 50% 150%, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 80%);
}

.hvr-shadow-radial:after {
  top: 100%;
  background: -webkit-radial-gradient(50% -50%, ellipse, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 80%);
  background: radial-gradient(ellipse at 50% -50%, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 80%);
}

.hvr-shadow-radial:hover:before,
.hvr-shadow-radial:focus:before,
.hvr-shadow-radial:active:before,
.hvr-shadow-radial:hover:after,
.hvr-shadow-radial:focus:after,
.hvr-shadow-radial:active:after {
  opacity: 1;
}

/* Float Shadow */
.hvr-float-shadow {
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  position: relative;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -webkit-transition-property: transform;
  transition-property: transform;
  will-change: transform;
}

.hvr-float-shadow:before {
  pointer-events: none;
  position: absolute;
  z-index: -1;
  content: '';
  top: 100%;
  left: 5%;
  height: 10px;
  width: 90%;
  opacity: 0;
  background: -webkit-radial-gradient(center, ellipse, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 80%);
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 80%);
  /* W3C */
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -webkit-transition-property: transform, opacity;
  transition-property: transform, opacity;
}

.hvr-float-shadow:hover,
.hvr-float-shadow:focus,
.hvr-float-shadow:active {
  -webkit-transform: translateY(-5px);
  transform: translateY(-5px);
  /* move the element up by 5px */
}

.hvr-float-shadow:hover:before,
.hvr-float-shadow:focus:before,
.hvr-float-shadow:active:before {
  opacity: 1;
  -webkit-transform: translateY(5px);
  transform: translateY(5px);
  /* move the element down by 5px (it will stay in place because it's attached to the element that also moves up 5px) */
}

/* Icon Wobble Horizontal */
@-webkit-keyframes hvr-icon-wobble-horizontal {
  16.65% {
    -webkit-transform: translateX(6px);
    transform: translateX(6px);
  }

  33.3% {
    -webkit-transform: translateX(-5px);
    transform: translateX(-5px);
  }

  49.95% {
    -webkit-transform: translateX(4px);
    transform: translateX(4px);
  }

  66.6% {
    -webkit-transform: translateX(-2px);
    transform: translateX(-2px);
  }

  83.25% {
    -webkit-transform: translateX(1px);
    transform: translateX(1px);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes hvr-icon-wobble-horizontal {
  16.65% {
    -webkit-transform: translateX(6px);
    transform: translateX(6px);
  }

  33.3% {
    -webkit-transform: translateX(-5px);
    transform: translateX(-5px);
  }

  49.95% {
    -webkit-transform: translateX(4px);
    transform: translateX(4px);
  }

  66.6% {
    -webkit-transform: translateX(-2px);
    transform: translateX(-2px);
  }

  83.25% {
    -webkit-transform: translateX(1px);
    transform: translateX(1px);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

.hvr-icon-wobble-horizontal {
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  will-change: transform;
}

.hvr-icon-wobble-horizontal .hvr-icon {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: transform;
}

.hvr-icon-wobble-horizontal:hover .hvr-icon,
.hvr-icon-wobble-horizontal:focus .hvr-icon,
.hvr-icon-wobble-horizontal:active .hvr-icon {
  -webkit-animation-name: hvr-icon-wobble-horizontal;
  animation-name: hvr-icon-wobble-horizontal;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(50%, 0, 0);
    transform: translate3d(50%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 50%, 0);
    transform: translate3d(0, 50%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -50%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);

  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 50%, 0);
    transform: translate3d(0, 50%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes dropdown {
  from {
    opacity: 0;
    height: 0;
  }

  to {
    opacity: 1;
    height: 100%;
  }
}

@keyframes fadeOutLeft {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes bounce {
  0% {
    transform: scale(1);
  }

  40% {
    transform: scale(1.08);
  }

  60% {
    transform: scale(0.95);
  }

  80% {
    transform: scale(1.03);
  }

  100% {
    transform: scale(1);
  }
}

.bounce-timing-function {
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 1.5);
}


.play-btn {
  display: inline-block;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: url('/assets/imgs/icon_play.svg') no-repeat center center;
  background-size: 100% 100%;
  cursor: pointer;
}

.icon-play-triangle {
  display: inline-block;
  background-color: #fff;
  width: 20px;
  height: 20px;
  --un-icon: url('../assets/imgs/icon_play_triangle.svg');
  mask: var(--un-icon) no-repeat;
  mask-size: 14px 20px;
  mask-position: center center;
}

.icon-chevron-down {
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.65);
  width: 20px;
  height: 20px;
  --un-icon: url('../assets/imgs/chevron-down.svg');
  mask: var(--un-icon) no-repeat;
  mask-size: 100% 100%;
}

.icon-external-link {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.97);
  width: 20px;
  height: 20px;
  --un-icon: url('../assets/imgs/external_link.svg');
  mask: var(--un-icon) no-repeat;
  mask-size: 100% 100%;
}

.icon-right-arrow {
  display: inline-block;
  background-color: #fff;
  width: 20px;
  height: 20px;
  --un-icon: url('../assets/imgs/icon_right_arrow.svg');
  mask: var(--un-icon) no-repeat;
  mask-size: 100% 100%;
  mask-position: center center;
}

.icon-x {
  display: inline-block;
  background-color: #fff;
  width: 18px;
  height: 18px;
  --un-icon: url('../assets/imgs/icon_x.svg');
  mask: var(--un-icon) no-repeat;
  mask-size: 100% 100%;
  mask-position: center center;
}

.icon-phone {
  display: inline-block;
  background-color: #fff;
  width: 20px;
  height: 20px;
  --un-icon: url('../assets/imgs/icon_phone_20x20.svg');
  mask: var(--un-icon) no-repeat;
  mask-size: 100% 100%;
}

.icon-mail {
  display: inline-block;
  background-color: #fff;
  width: 20px;
  height: 20px;
  --un-icon: url('../assets/imgs/icon_mail_20x20.svg');
  mask: var(--un-icon) no-repeat;
  mask-size: 100% 100%;
}

.icon-location {
  display: inline-block;
  background-color: #fff;
  width: 20px;
  height: 20px;
  --un-icon: url('../assets/imgs/icon_location_20x20.svg');
  mask: var(--un-icon) no-repeat;
  mask-size: 100% 100%;
}

.icon-doc {
  display: inline-block;
  background: url('../assets/imgs/icon_doc.svg') no-repeat center center;
  background-size: 100% 100%;
  width: 40px;
  height: 40px;
}

.icon-model {
  display: inline-block;
  background: url('../assets/imgs/icon_model.svg') no-repeat center center;
  background-size: 100% 100%;
  width: 40px;
  height: 40px;
}

.icon-download {
  display: inline-block;
  background-color: #fff;
  width: 20px;
  height: 20px;
  --un-icon: url('../assets/imgs/icon_download.svg');
  mask: var(--un-icon) no-repeat;
  mask-size: 100% 100%;
}