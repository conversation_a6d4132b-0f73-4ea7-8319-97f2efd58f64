declare module 'bootstrap';
declare module '*.svg' {
  import type { DefineComponent } from 'vue-demi';

  const component: DefineComponent;

  export default component;
}

declare module 'aos' {
  export interface AOSOptions {
    disable?: boolean; // accepts following values: 'phone', 'tablet', 'mobile', boolean, expression or function
    startEvent?: string; // name of the event dispatched on the document, that AOS should initialize on
    initClassName?: boolean | string; // class applied after initialization
    animatedClassName?: string; // class applied on animation
    useClassNames?: boolean; // if true, will add content of `data-aos` as classes on scroll
    disableMutationObserver?: boolean; // disables automatic mutations' detections (advanced)
    debounceDelay?: number; // the delay on debounce used while resizing window (advanced)
    throttleDelay?: number; // the delay on throttle used while scrolling the page (advanced)


    // Settings that can be overridden on per-element basis, by `data-aos-*` attributes:
    offset?: number; // offset (in px) from the original trigger point
    delay?: number; // values from 0 to 3000, with step 50ms
    duration?: number; // values from 0 to 3000, with step 50ms
    easing?: string; // default easing for AOS animations
    once?: boolean; // whether animation should happen only once - while scrolling down
    mirror?: boolean; // whether elements should animate out while scrolling past them
    anchorPlacement?: string;
  };

  function init(options?: AOSOptions): void;
  function refresh(initialize?: boolean): void;
  function refreshHard(): void;

  export default {
    init,
    refresh,
    refreshHard,
  }
}

declare var bs: any;
