export function unwrap<R = any>(res: API.BaseRes<R>) {
  if (res.code === 0) {
    return res.data;
  }

  throw new Error(res.msg);
}
export function formatTime(timestamp: number, format: string = 'yyyy-MM-dd HH:mm:ss') {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return format
    .replace('yyyy', year.toString())
    .replace('MM', month)
    .replace('dd', day)
   .replace('HH', hours)
   .replace('mm', minutes)
   .replace('ss', seconds);
}

// 累加函数：计算1到n的和（1+2+3+...+n）
export function cumulativeSum(n: number): number {
  // if (n < 1) throw new Error('输入必须为正整数');
  return n * (n + 1) / 2;
}

